<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:link-package-dmo',
    description: 'Établit les relations entre les packages et les DMO'
)]
class LinkPackageDMOCommand extends Command
{
    protected static $defaultName = 'app:link-package-dmo';

    public function __construct(
        private EntityManagerInterface $em,
        #[ConnectionName('legacy')]
        private Connection $legacyConnection
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Début de l\'établissement des relations Package ↔ DMO...</info>');

        // 1. Vérifier les données disponibles
        $this->checkAvailableData($output);

        // 2. Nettoyer les relations existantes
        $this->cleanExistingRelations($output);

        // 3. Établir les nouvelles relations via SQL direct
        $this->establishRelationsDirectSQL($output);

        $output->writeln('<info>Établissement des relations terminé avec succès !</info>');
        return Command::SUCCESS;
    }

    /**
     * Vérifier les données disponibles
     */
    private function checkAvailableData(OutputInterface $output): void
    {
        $output->writeln('<info>Vérification des données disponibles...</info>');

        // Compter les DMO
        $dmoCount = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM dmo WHERE dmo IS NOT NULL AND dmo != ""');
        $output->writeln("<comment>DMO disponibles: $dmoCount</comment>");

        // Compter les packages
        $packageCount = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM released_package');
        $output->writeln("<comment>Packages disponibles: $packageCount</comment>");

        // Afficher quelques exemples de DMO
        $sampleDmos = $this->em->getConnection()->fetchAllAssociative('SELECT id, dmo FROM dmo WHERE dmo IS NOT NULL AND dmo != "" LIMIT 5');
        $output->writeln('<comment>Exemples de DMO:</comment>');
        foreach ($sampleDmos as $dmo) {
            $output->writeln('<comment>  - ID: ' . $dmo['id'] . ', DMO: ' . $dmo['dmo'] . '</comment>');
        }
    }

    /**
     * Nettoyer les relations existantes
     */
    private function cleanExistingRelations(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des relations existantes...</info>');

        $deleted = $this->em->getConnection()->executeStatement('DELETE FROM released_package_dmo');
        $output->writeln("<comment>$deleted relations supprimées.</comment>");
    }

    /**
     * Établir les relations Package ↔ DMO via requête directe entre bases
     */
    private function establishRelationsDirectSQL(OutputInterface $output): void
    {
        $output->writeln('<info>Établissement des relations Package ↔ DMO via requête directe...</info>');

        // Utiliser directement l'approche alternative qui fonctionne
        $output->writeln('<comment>Utilisation de l\'approche par lots...</comment>');
        $this->establishRelationsAlternative($output);

        // Afficher un échantillon des relations créées
        $this->showSampleRelations($output);
    }

    /**
     * Approche alternative pour créer les relations
     */
    private function establishRelationsAlternative(OutputInterface $output): void
    {
        $output->writeln('<info>Approche alternative : traitement par lots...</info>');

        // Récupérer tous les DMO
        $dmos = $this->em->getConnection()->fetchAllAssociative(
            'SELECT id, dmo FROM dmo WHERE dmo IS NOT NULL AND dmo != ""'
        );

        $relationsCreated = 0;
        $batchSize = 100;
        $batches = array_chunk($dmos, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $output->writeln('<comment>Traitement du lot ' . ($batchIndex + 1) . '/' . count($batches) . '...</comment>');

            foreach ($batch as $dmo) {
                try {
                    // Chercher les packages correspondants dans l'ancienne base
                    $linkedPackages = $this->legacyConnection->fetchAllAssociative(
                        'SELECT Rel_Pack_Num FROM tbl_released_package WHERE DMO = ?',
                        [$dmo['dmo']]
                    );

                    foreach ($linkedPackages as $package) {
                        $packageId = $package['Rel_Pack_Num'];

                        // Vérifier que le package existe dans la nouvelle base
                        $packageExists = $this->em->getConnection()->fetchOne(
                            'SELECT COUNT(*) FROM released_package WHERE id = ?',
                            [$packageId]
                        );

                        if ($packageExists > 0) {
                            $this->em->getConnection()->executeStatement(
                                'INSERT IGNORE INTO released_package_dmo (released_package_id, dmo_id) VALUES (?, ?)',
                                [$packageId, $dmo['id']]
                            );
                            $relationsCreated++;
                        }
                    }

                } catch (\Exception $e) {
                    $output->writeln('<error>Erreur pour DMO ' . $dmo['dmo'] . ': ' . $e->getMessage() . '</error>');
                }
            }
        }

        $output->writeln('<info>' . $relationsCreated . ' relations créées via approche alternative.</info>');
    }



    /**
     * Afficher un échantillon des relations créées
     */
    private function showSampleRelations(OutputInterface $output): void
    {
        $sampleRelations = $this->em->getConnection()->fetchAllAssociative(
            'SELECT rpd.released_package_id, rpd.dmo_id, d.dmo
             FROM released_package_dmo rpd
             JOIN dmo d ON rpd.dmo_id = d.id
             LIMIT 10'
        );

        if (!empty($sampleRelations)) {
            $output->writeln('<comment>Échantillon des relations créées:</comment>');
            foreach ($sampleRelations as $relation) {
                $output->writeln('<comment>  - Package ID: ' . $relation['released_package_id'] . ' ↔ DMO: ' . $relation['dmo'] . '</comment>');
            }
        } else {
            $output->writeln('<comment>Aucune relation trouvée dans l\'échantillon.</comment>');
        }

        // Afficher le total
        $totalRelations = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM released_package_dmo');
        $output->writeln('<info>Total des relations dans la base: ' . $totalRelations . '</info>');
    }

}

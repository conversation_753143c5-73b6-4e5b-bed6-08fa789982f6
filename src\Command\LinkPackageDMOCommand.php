<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:link-package-dmo',
    description: 'Établit les relations entre les packages et les DMO'
)]
class LinkPackageDMOCommand extends Command
{
    protected static $defaultName = 'app:link-package-dmo';

    public function __construct(
        #[ConnectionName('legacy')]
        private Connection $oldDb,
        private EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Début de l\'établissement des relations Package ↔ DMO...</info>');

        // 1. Nettoyer les relations existantes
        $this->cleanExistingRelations($output);

        // 2. Établir les nouvelles relations
        $this->establishRelations($output);

        $output->writeln('<info>Établissement des relations terminé avec succès !</info>');
        return Command::SUCCESS;
    }

    /**
     * Nettoyer les relations existantes
     */
    private function cleanExistingRelations(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des relations existantes...</info>');
        
        $deleted = $this->em->getConnection()->executeStatement('DELETE FROM released_package_dmo');
        $output->writeln("<comment>$deleted relations supprimées.</comment>");
    }

    /**
     * Établir les relations Package ↔ DMO
     */
    private function establishRelations(OutputInterface $output): void
    {
        $output->writeln('<info>Établissement des relations Package ↔ DMO...</info>');

        // Récupérer tous les DMO avec leur identifiant DMO (champ texte)
        $sql = 'SELECT id, dmo FROM dmo WHERE dmo IS NOT NULL AND dmo != ""';
        $dmos = $this->em->getConnection()->fetchAllAssociative($sql);

        if (empty($dmos)) {
            $output->writeln('<info>Aucun DMO avec identifiant DMO trouvé.</info>');
            return;
        }

        $output->writeln('<info>Traitement de ' . count($dmos) . ' DMO...</info>');

        // Construire le mapping des packages (Rel_Pack_Num -> ID)
        $packageMapping = $this->buildPackageMapping($output);

        $relationsToCreate = [];
        $foundRelations = 0;
        $notFoundDmos = [];

        // Chercher les relations via le champ DMO (texte) dans tbl_released_package
        foreach ($dmos as $dmo) {
            $dmoIdentifier = $dmo['dmo']; // Le champ DMO (texte) de la nouvelle table
            $newDmoId = $dmo['id'];

            try {
                // Chercher dans tbl_released_package avec le champ DMO (texte)
                $linkedPackagesSql = 'SELECT Rel_Pack_Num FROM tbl_released_package WHERE DMO = ?';
                $linkedPackages = $this->oldDb->fetchAllAssociative($linkedPackagesSql, [$dmoIdentifier]);

                if (!empty($linkedPackages)) {
                    foreach ($linkedPackages as $package) {
                        $relPackNum = $package['Rel_Pack_Num'];
                        if (isset($packageMapping[$relPackNum])) {
                            $relationsToCreate[] = [
                                'released_package_id' => $packageMapping[$relPackNum],
                                'dmo_id' => $newDmoId
                            ];
                            $foundRelations++;
                        } else {
                            $output->writeln('<comment>Package ' . $relPackNum . ' non trouvé dans le mapping pour DMO: ' . $dmoIdentifier . '</comment>');
                        }
                    }
                } else {
                    $notFoundDmos[] = $dmoIdentifier;
                }
            } catch (\Exception $e) {
                $output->writeln('<error>Erreur lors de la recherche pour DMO ' . $dmoIdentifier . ': ' . $e->getMessage() . '</error>');
            }
        }

        $output->writeln('<info>Relations trouvées: ' . $foundRelations . '</info>');
        if (!empty($notFoundDmos)) {
            $output->writeln('<comment>' . count($notFoundDmos) . ' DMO sans package associé: ' . implode(', ', array_slice($notFoundDmos, 0, 10)) . (count($notFoundDmos) > 10 ? '...' : '') . '</comment>');
        }

        if (empty($relationsToCreate)) {
            $output->writeln('<info>Aucune relation Package ↔ DMO à créer.</info>');
            return;
        }

        // Insertion des relations en batch
        $this->insertRelationsBatch($relationsToCreate, $output);

        $output->writeln('<info>' . count($relationsToCreate) . ' relations Package ↔ DMO créées.</info>');
    }

    /**
     * Construire le mapping des packages (Rel_Pack_Num -> ID)
     */
    private function buildPackageMapping(OutputInterface $output): array
    {
        $output->writeln('<info>Construction du mapping des packages...</info>');

        $sql = 'SELECT id FROM released_package';
        $packages = $this->em->getConnection()->fetchAllAssociative($sql);

        $mapping = [];
        foreach ($packages as $package) {
            // Dans la migration, l'ID du package = Rel_Pack_Num de l'ancienne base
            $mapping[$package['id']] = $package['id'];
        }

        $output->writeln('<info>Mapping construit pour ' . count($mapping) . ' packages.</info>');
        return $mapping;
    }

    /**
     * Insérer les relations en batch
     */
    private function insertRelationsBatch(array $relations, OutputInterface $output): void
    {
        $batchSize = 500;
        $batches = array_chunk($relations, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $relation) {
                $values[] = '(?, ?)';
                $params = array_merge($params, [
                    $relation['released_package_id'],
                    $relation['dmo_id']
                ]);
            }

            $sql = 'INSERT IGNORE INTO released_package_dmo (released_package_id, dmo_id)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch relations ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' relations)</comment>');
        }
    }
}

<?php

require_once 'vendor/autoload.php';
use Symfony\Component\Dotenv\Dotenv;

$dotenv = new Dotenv();
$dotenv->load('.env');
$dotenv->load('.env.local');

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['LEGACY_DB_HOST'] . ';port=' . $_ENV['LEGACY_DB_PORT'] . ';charset=utf8mb4',
        $_ENV['LEGACY_DB_USER'],
        $_ENV['LEGACY_DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Récupérer la structure de la table
    $stmt = $pdo->query('DESCRIBE db_scm.tbl_fxxx');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Structure de la table tbl_fxxx:\n";
    foreach ($columns as $column) {
        echo sprintf("- %s: %s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'] === 'YES' ? '(nullable)' : '(not null)'
        );
    }
    
    // Récupérer quelques exemples de données
    $stmt = $pdo->query('SELECT * FROM db_scm.tbl_fxxx LIMIT 10');
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nExemples de données:\n";
    foreach ($data as $row) {
        echo json_encode($row, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n---\n";
    }
    
    // Compter le nombre total d'enregistrements
    $stmt = $pdo->query('SELECT COUNT(*) as total FROM db_scm.tbl_fxxx');
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\nNombre total d'enregistrements: " . $count['total'] . "\n";
    
} catch (Exception $e) {
    echo 'Erreur: ' . $e->getMessage() . "\n";
}

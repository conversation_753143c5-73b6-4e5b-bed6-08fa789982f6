/**
 * Gestionnaire de tags pour les matériaux multiples
 */
class MaterialTags {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            inputName: 'materials[]',
            placeholder: 'Rechercher un matériau...',
            maxTags: 10,
            ...options
        };

        this.materials = new Map();
        this.input = null;
        this.datalist = null;
        this.tagsContainer = null;

        this.init();
    }

    init() {
        this.createStructure();
        this.initDatalist();
        this.loadExistingMaterials();
    }

    createStructure() {
        this.container.innerHTML = `
            <div class="material-tags-wrapper">
                <div class="material-tags-container mb-2"></div>
                <div class="material-input-container">
                    <input type="text"
                           class="form-control"
                           placeholder="${this.options.placeholder}"
                           autocomplete="off">
                </div>
            </div>
        `;

        this.tagsContainer = this.container.querySelector('.material-tags-container');
        this.input = this.container.querySelector('input');
    }

    initDatalist() {
        // Vérifier que MaterialDatalist est disponible
        if (typeof MaterialDatalist === 'undefined') {
            return;
        }

        // Utiliser notre classe MaterialDatalist
        this.materialDatalist = new MaterialDatalist(this.input);

        // Écouter les sélections de matériaux
        this.input.addEventListener('materialSelected', (e) => {
            this.addMaterial(e.detail);
            this.input.value = ''; // Vider l'input après sélection
        });
    }

    addMaterial(materialData) {
        // Vérifier si le matériau n'est pas déjà ajouté
        if (this.materials.has(materialData.id)) {
            return;
        }

        // Vérifier la limite
        if (this.materials.size >= this.options.maxTags) {
            alert(`Vous ne pouvez pas ajouter plus de ${this.options.maxTags} matériaux.`);
            return;
        }

        // Ajouter le matériau à la collection
        this.materials.set(materialData.id, materialData);

        // Créer le tag visuel
        this.createTag(materialData);

        // Créer l'input hidden pour le formulaire
        this.createHiddenInput(materialData);

        // Émettre un événement
        this.container.dispatchEvent(new CustomEvent('materialAdded', {
            detail: materialData
        }));
    }

    createTag(materialData) {
        const tag = document.createElement('span');
        tag.className = 'badge bg-primary me-2 mb-2 material-tag';
        tag.dataset.materialId = materialData.id;
        tag.innerHTML = `
            ${materialData.reference}
            <button type="button" class="btn-close btn-close-white ms-2" aria-label="Supprimer"></button>
        `;

        // Ajouter l'événement de suppression
        const closeBtn = tag.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            this.removeMaterial(materialData.id);
        });

        this.tagsContainer.appendChild(tag);
    }

    createHiddenInput(materialData) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = this.options.inputName;
        hiddenInput.value = materialData.id;
        hiddenInput.dataset.materialId = materialData.id;

        this.container.appendChild(hiddenInput);
    }

    removeMaterial(materialId) {
        // Supprimer de la collection
        this.materials.delete(materialId);

        // Supprimer le tag visuel
        const tag = this.tagsContainer.querySelector(`[data-material-id="${materialId}"]`);
        if (tag) {
            tag.remove();
        }

        // Supprimer l'input hidden
        const hiddenInput = this.container.querySelector(`input[data-material-id="${materialId}"]`);
        if (hiddenInput) {
            hiddenInput.remove();
        }

        // Émettre un événement
        this.container.dispatchEvent(new CustomEvent('materialRemoved', {
            detail: { id: materialId }
        }));
    }

    loadExistingMaterials() {
        // Charger les matériaux existants depuis les inputs hidden
        const existingInputs = this.container.querySelectorAll(`input[name="${this.options.inputName}"]`);

        existingInputs.forEach(input => {
            const materialId = input.value;
            const materialReference = input.dataset.materialReference || `Material ${materialId}`;

            const materialData = {
                id: materialId,
                reference: materialReference,
                label: materialReference
            };

            this.materials.set(materialId, materialData);
            this.createTag(materialData);
        });
    }

    getMaterials() {
        return Array.from(this.materials.values());
    }

    setMaterials(materials) {
        // Vider les matériaux existants
        this.clear();

        // Ajouter les nouveaux matériaux
        materials.forEach(material => {
            this.addMaterial(material);
        });
    }

    clear() {
        this.materials.clear();
        this.tagsContainer.innerHTML = '';

        // Supprimer tous les inputs hidden
        const hiddenInputs = this.container.querySelectorAll(`input[name="${this.options.inputName}"]`);
        hiddenInputs.forEach(input => input.remove());
    }
}

// Fonction d'initialisation globale
window.initMaterialTags = function(selector, options = {}) {
    const containers = document.querySelectorAll(selector);
    const instances = [];

    containers.forEach(container => {
        instances.push(new MaterialTags(container, options));
    });

    return instances;
};

// Auto-initialisation pour les éléments avec la classe 'material-tags'
document.addEventListener('DOMContentLoaded', function() {
    window.initMaterialTags('.material-tags');
});

<?php

require_once 'vendor/autoload.php';
use Symfony\Component\Dotenv\Dotenv;

$dotenv = new Dotenv();
$dotenv->load('.env');
$dotenv->load('.env.local');

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['LEGACY_SCM_DB_HOST'] . ';port=' . $_ENV['LEGACY_SCM_DB_PORT'] . ';dbname=' . $_ENV['LEGACY_SCM_DB_NAME'] . ';charset=utf8mb4',
        $_ENV['LEGACY_SCM_DB_USER'],
        $_ENV['LEGACY_SCM_DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connexion SCM réussie !\n";
    
    // Test de la table tbl_fxxx
    $stmt = $pdo->query('SELECT COUNT(*) as total FROM tbl_fxxx');
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Nombre de matériaux dans tbl_fxxx: " . $count['total'] . "\n";
    
    // Quelques exemples
    $stmt = $pdo->query('SELECT * FROM tbl_fxxx LIMIT 3');
    $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nExemples de matériaux:\n";
    foreach ($materials as $material) {
        echo "- {$material['fxxx_ref']}: {$material['fxxx_description']}\n";
    }
    
} catch (Exception $e) {
    echo 'Erreur: ' . $e->getMessage() . "\n";
}

{% extends 'base.html.twig' %}

{% block title %}Package {{ package.id }}{% endblock %}

{% block body %}
<style>
td{
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

th{
    text-align: center;
    vertical-align: middle;
    background-color: #004080!important;
    color: white!important;
    border: none!important;
    font-size: 10px!important;
}

.badge-hover:hover{
    background-color: #004080!important;
    transition: background-color 0.3s ease;
}

.badge-hover{
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.fw-500{
    font-weight: 500;
}

label{
    white-space: nowrap;
    user-select: none;
}

#document-form label{
    min-width: 15rem!important;
    width: 15rem!important;
    max-width: 15rem!important;
}

.form-label-sm{
    font-size: .875rem;
}

/* Styles pour le modal des commentaires */
.comment-item {
    background-color: #f8f9fa;
    border-left: 4px solid #009BFF;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-state {
    background-color: #009BFF;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-content {
    color: #333;
    line-height: 1.5;
    margin-top: 8px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.comments-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

/* Styles pour le formulaire d'ajout de commentaire */
.modal-footer.custom-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#newCommentText {
    resize: vertical;
    min-height: 38px;
}

#addCommentBtn {
    background-color: #009BFF;
    border-color: #009BFF;
    height: 38px;
}

#addCommentBtn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.comment-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Modale personnalisée */
.modal-dialog.modal-lg {
    max-width: 900px;
}

.modal-content.custom-modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.modal-header.custom-modal-header {
    background-color: #004080;
    color: #fff;
    border-bottom: none;
}

.modal-header.custom-modal-header .btn-close {
    filter: invert(100%);
}

/* Retirer la transparence des tooltips pour une meilleure lisibilité */
.tooltip .tooltip-inner {
    background-color: rgba(0, 0, 0, 0.95) !important;
    opacity: 1 !important;
    max-width: 400px;
    overflow-y: auto;
    white-space: normal;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before,
.tooltip.bs-tooltip-bottom .tooltip-arrow::before,
.tooltip.bs-tooltip-start .tooltip-arrow::before,
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-color: rgba(0, 0, 0, 0.95) transparent !important;
}

.first{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    width: 90%;
}

.second{
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    width: 15%;
}

.selected-row td{
    background-color:rgba(62, 156, 250, 0.45)!important;
    border: 1px rgba(62, 156, 250, 0.45)!important;
}


</style>

<div class="m-0">
    <div class="card rounded-0 border-0">
        <div style="background-color: #004080;" class="card-header border-0 text-white d-flex justify-content-between align-items-center rounded-0">
            <h4 class="card-title p-0 mb-0">Update Package - {{ package.id }}</h4>
            <div>
                <button class="btn btn-sm btn-success" id="csv-modal" style="display: none;"></button>
                <button class="btn btn-sm btn-primary active" id="csv-package" onclick="document.getElementById('csv-file').click();"><i class="fas fa-file-csv"></i> Add Package</button>
                <input type="file" id="csv-file" accept=".csv" style="display: none;">
            </div>
        </div>
        <div class="card-body row mx-0 p-0">
            {# DOCUMENT FORM COL #}
            <input type="hidden" id="document_id" name="document_id" value="">
            <div class="border-end col-md-4" id="document-form">
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="reference">Reference Article (SAP)</label>
                    <div class="d-flex align-items-center w-100">
                        <input type="text" class="form-control form-control-sm first" id="reference" name="reference" placeholder="Reference" required>
                        <input type="text" class="form-control form-control-sm second" id="refRev" name="refRev" placeholder="Rev" required>
                    </div>
                </div>
                <input type="hidden" id="idAletiq" name="idAletiq" placeholder="ID ALETIQ" >
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="prodDraw">Product Drawing</label>
                    <div class="d-flex align-items-center w-100">
                        <input type="text" class="form-control form-control-sm first" id="prodDraw" name="prodDraw" placeholder="ProdDraw" required>
                        <input type="text" class="form-control form-control-sm second" id="prodDrawRev" name="prodDrawRev" placeholder="Rev" required>
                    </div>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="alias">Alias</label>
                    <div class="w-100 position-relative">
                        <input type="text" class="form-control form-control-sm" id="alias" name="alias" placeholder="Alias" maxlength="40">
                        <small id="charCount" class="position-absolute end-0 top-0 text-muted mt-2 me-2" style="font-size: 0.7rem;">0/40</small>
                    </div>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="refTitleFra">Title</label>
                    <div class="w-100 position-relative">
                        <input type="text" class="form-control form-control-sm" id="refTitleFra" name="refTitleFra" placeholder="Title" maxlength="40" required>
                        <small id="titleCharCount" class="position-absolute end-0 top-0 text-muted mt-2 me-2" style="font-size: 0.7rem;">0/40</small>
                    </div>
                </div>
                <!-- Messages d'alerte pour les limites de caractères -->
                <div id="charLimitAlert" class="alert alert-warning alert-dismissible fade mt-2" role="alert" style="display: none; font-size: 0.8rem; padding: 0.5rem;">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <span id="alertMessage"></span>
                    <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert" aria-label="Close" style="font-size: 0.7rem;"></button>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="action">Action</label>
                    <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="action" name="action" required title="Action" >
                        <option value="Modification">Modification</option>
                        <option value="Suppression">Suppression</option>
                        <option value="Creation">Création</option>
                    </select>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="docType">Type</label>
                    <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="docType" name="docType" required title="Type" >
                        <option value ="PUR" title="PUR">PUR - Purchased </option>
                        <option value ="MOLD" title="MOLD">MOLD - Molded Part (Internal or External)</option>
                        <option value ="MACH" title="MACH">MACH - Machined Part (Internal or External)</option>
                        <option value ="DOC" title="DOC">DOC - PC, ID, E-, AD</option>
                        <option value ="ASSY" title="ASSY">ASSY - Marking, BoM, GA, Assembly, FT for ZPF BoM driven by FT</option>
                    </select>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="materialType">Material Type</label>
                    <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="materialType" name="materialType" required title="Material Type" >
                        <option value ="SEMI-FINISHED PRODUCT" >SEMI-FINISHED PRODUCT</option>
                        <option value ="RAW MATERIAL" >RAW MATERIAL</option>
                        <option value ="PACKAGING" >PACKAGING</option>
                        <option value ="NON VALUATED MATERIAL" >NON VALUATED MATERIAL</option>
                        <option value ="LITERATURE" >LITERATURE</option>
                        <option value ="FINISHED PRODUCT" >FINISHED PRODUCT</option>
                    </select>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="inventoryImpact">Inventory Impact</label>
                    <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="inventoryImpact" name="inventoryImpact" required title="Inventory Impact" >
                        <option value="TO BE UPDATED" test="Ongoing orders in SCM, orders at suppliers or parts in stock at SCM must be reworkedn ">TO BE UPDATED</option>
                        <option value="TO BE SCRAPPED" test="Every parts in stock, being used in assembly, or being manufactured/ordered must be scrapped ">TO BE SCRAPPED</option>
                        <option value="NO IMPACT" test="No impact on the current stock or any ongoing orders or manufacturing">NO IMPACT</option>
                    </select>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="ex">Ex</label>
                    <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="ex" name="ex" required title="Ex">
                        <option value="NO">NO</option>
                        <option value="IECEX">IECEX</option>
                        <option value="CSA">CSA</option>
                        <option value="ATEX">ATEX</option>
                    </select>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="custDrawing">Cust. Drawing</label>
                    <div class="d-flex align-items-center w-100">
                        <input type="text" class="form-control form-control-sm first" id="custDrawing" name="custDrawing" placeholder="Cust. Drawing" >
                        <input type="text" class="form-control form-control-sm second" id="custDrawingRev" name="custDrawingRev" placeholder="Rev" >
                    </div>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="weight">Weight in air</label>
                    <div class="d-flex align-items-center w-100">
                        <input type="number" class="form-control form-control-sm first" id="weight" name="weight" placeholder="Weight" required>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="weightUnit" name="weightUnit" required title="Unit" >
                            <option value=""></option>
                            <option value="g">g</option>
                            <option value="kg">kg</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <label class="fw-500 form-label-sm" for="materials">Matériaux</label>
                    <div class="material-tags" id="materials-container">
                        <!-- Les matériaux existants seront chargés ici dynamiquement -->
                    </div>
                    <small class="form-text text-muted">Tapez pour rechercher et sélectionner des matériaux</small>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="platingSurface">Plating Surface</label>
                    <div class="d-flex align-items-center w-100">
                        <input type="text" class="form-control form-control-sm first" id="platingSurface" name="platingSurface" placeholder="Plating Surface" >
                        <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="platingSurfaceUnit" name="platingSurfaceUnit" title="Unit" >
                            <option value=""></option>
                            <option value="mm²">mm²</option>
                            <option value="cm²">cm²</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2 d-flex align-items-center">
                    <label class="fw-500 form-label-sm" for="internalMachRec">Recommanded Inhouse Manuf.</label>
                    <div class="w-100">
                        <input type="checkbox" class="form-check-input mt-2 me-2" id="internalMachRec" name="internalMachRec" placeholder="internalMachRec" style="transform: scale(1.5);">
                    </div>
                </div>
                <div class="mt-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <label class="fw-500 form-label-sm" for="comments">Commentaire</label>
                        <button type="button"
                                class="btn btn-outline-primary btn-sm"
                                id="viewPreviousCommentsBtn"
                                onclick="showPreviousCommentsForCurrentDocument()"
                                style="font-size: 0.75rem; padding: 2px 8px; display: none;">
                            <i class="fas fa-eye me-1"></i>
                            Voir antérieurs (<span id="commentsCount">0</span>)
                        </button>
                    </div>
                    <textarea class="form-control form-control-sm" id="comments" name="comments" placeholder="Commentaire" rows="3"></textarea>
                </div>
            </div>
            {# HTS ECNN RDO COL #}
            <div class="col-md-2 border-end px-0">
                <div class="mt-2 px-2 pb-2 border-bottom">
                    <label class="fw-500 w-100 form-label-sm" for="hts">Code HTS</label>
                    <div class="ms-2" id="Q1_DIV">
                        <label class="fw-500 w-100 form-label-sm" for="Q1" >The reference is</label>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="Q1" name="Q1" required title="Question 1" data-live-search="true" data-size="10">
                        </select>
                    </div>
                    <div class="ms-2" id="Q2_DIV" style="display: none;">
                        <label class="fw-500 w-100 form-label-sm" id="Q2_LABEL" for="Q2" ></label>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="Q2" name="Q2" required title="Question 2" data-live-search="true" data-size="10">
                        </select>
                    </div>
                    <div class="ms-2" id="Q3_DIV" style="display: none;">
                        <label class="fw-500 w-100 form-label-sm" id="Q3_LABEL" for="Q3" ></label>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="Q3" name="Q3" required title="Question 3" data-live-search="true" data-size="10">
                        </select>
                    </div>
                    <input type="text" class="form-control form-control-sm mt-2" id="hts" name="hts" placeholder="Code HTS" required>
                </div>
                <div class="mt-2 px-2 pb-2 border-bottom">
                    <label class="fw-500 w-100 form-label-sm" for="hts">Code ECCN / RDO</label>
                    <div class="ms-2" id="Q1_eccn_DIV">
                        <label class="fw-500 w-100 form-label-sm" for="Q1_eccn" >The reference is</label>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="Q1_eccn" name="Q1_eccn" required title="Question 1" data-live-search="true" data-size="10">
                        </select>
                    </div>
                    <div class="ms-2" id="Q2_eccn_DIV" style="display: none;">
                        <label class="fw-500 w-100 form-label-sm" id="Q2_eccn_LABEL" for="Q2_eccn" ></label>
                        <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="Q2_eccn" name="Q2_eccn" required title="Question 2" data-live-search="true" data-size="10">
                        </select>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <input type="text" class="form-control form-control-sm mt-2" id="eccn" name="eccn" placeholder="Code eccn" required>
                        <input type="text" class="form-control form-control-sm mt-2" id="rdo" name="rdo" placeholder="Code RDO" required>
                    </div>
                </div>
                <div class="px-2 pb-2 pt-2 d-flex align-items-center gap-2">
                    <button class="btn btn-sm btn-outline-primary w-100" id="add-document"><i class="fas fa-plus"></i> Ajouter</button>

                    <button class="btn btn-sm btn-danger w-100" id="delete-document" style="display: none;"><i class="fas fa-trash"></i> Supprimer</button>
                </div>
            </div>
            <div class="col-md-6 p-0 row-cols-1 mx-0" style="background-color: #F1F3F5;">
                {# PREVIEW row #}
                <iframe id="aletiq_preview" class="col" style="width:100%;height:70%;" src=""></iframe>
                {# COL PACKAGE #}
                <div class="border-top col pb-3" style="height: 30%;background-color: #EFF1F2;" >
                    <h6 class="fw-500 text-muted m-0">Package info</h6>
                    <div class="d-flex align-items-center justify-content-around">
                        <div class="form-group">
                            <label class="fw-500" for="owner">Propriétaire</label>
                            <p class="fw-bold m-0 ms-2" id="owner"><i class="fas fa-user"></i> {{ package.owner }}</p>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="activity">Activité</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="activity" name="activity" required title="Activité" >
                                <option value="OSI">OSI</option>
                                <option value="MOB_INDUS">MOB_INDUS</option>
                                <option value="MOB_AERO">MOB_AERO</option>
                                <option value="METHOD">METHOD</option>
                                <option value="ENERGY_SIGNAL">ENERGY_SIGNAL</option>
                                <option value="ENERGY_RENEW">ENERGY_RENEW</option>
                                <option value="ENERGY_POWER">ENERGY_POWER</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="ex-package">Ex</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="ex-package" name="ex-package" required title="Ex" >
                                <option value="NO">NO</option>
                                <option value="IECEX">IECEX</option>
                                <option value="CSA">CSA</option>
                                <option value="ATEX">ATEX</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="dmo">DMO</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="dmo" name="dmo[]" required multiple title="DMO" data-live-search="true" data-size="10" data-selected-text-format="count > 2">
                                {% for otp, dmoList in dmobyProjects %}
                                    <optgroup label="{{ otp }}">
                                        {% for dmo in dmoList %}
                                            <option value="{{ dmo.id }}" data-project="{{ otp }}">
                                                {{ dmo.id_dmo }}
                                            </option>
                                        {% endfor %}
                                    </optgroup>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="project">Projet</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="project" name="project" required data-live-search="true" data-size="10">
                                {% for project in projects %}
                                    {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                                        <option value="{{ project.id }}" selected>{{ project.otp }}</option>
                                    {% else %}
                                        <option value="{{ project.id }}">{{ project.otp }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mx-0 mt-2">
                        <div class="col-md-10">
                            <label class="fw-500" for="description">Description</label>
                            <textarea class="form-control form-control-sm" id="description" name="description" rows="3" required style="resize: none;">{{ package.description }}</textarea>
                        </div>
                        <div class="col-md-2 d-flex flex-column justify-content-end gap-2">
                            <button class="btn btn-sm btn-outline-secondary" id="update">Update</button>
                            <button class="btn btn-sm btn-success" id="release">Release</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 p-0" id="div-documents">
                <table class="table table-hover table-sm table-bordered mb-0" id="table-documents">
                    <thead>
                        <tr id="entetes">
                            <th>Action</th>
                            <th colspan="2">Reference</th>
                            <th>ID_ALETIQ</th>
                            <th colspan="2">Product Drawing</th>
                            <th>Title</th>
                            <th>Alias</th>
                            <th>Cust. Drawing</th>
                            <th colspan="2">Doc Type</th>
                            <th>Mat Type</th>
                            <th>Inventory Imp.</th>
                            <th>Ex</th>
                            <th colspan="2">Weight</th>
                            <th colspan="2">Plat. Surface</th>
                            <th>Material</th>
                            <th>ECCN</th>
                            <th>RDO</th>
                            <th>HTS</th>
                            <th>Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in package.documents %}
                        <tr class="document_row" id="document_{{ document.id }}">
                            <td class="action" >{{ document.action }}</td>
                            <td class="reference" >{{ document.reference }}</td>
                            <td class="refRev" >{{ document.refRev }}</td>
                            <td class="idAletiq" >{{ document.idAletiq }}</td>
                            <td class="prodDraw" >
                                <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                    target="_blank"
                                    class="badge bg-primary preview-tooltip">
                                    {{ document.prodDraw }}
                                </a>
                            </td>
                            <td class="prodDrawRev" >{{ document.prodDrawRev }}</td>
                            <td class="refTitleFra" >{{ document.refTitleFra }}</td>
                            <td class="alias" >{{ document.alias }}</td>
                            <td class="custDrawing" >{{ document.custDrawing }}</td>
                            <td class="docType" >{{ document.docType }}</td>
                            <td class="internalMachRec" >{% if document.internalMachRec == 1 %}<img src="{{ asset('scm.png') }}" alt="scm" style="height: 15px;">{% endif %}</td>
                            <td class="materialType" >{{ document.materialType }}</td>
                            <td class="inventoryImpact" >{{ document.inventoryImpact }}</td>
                            <td class="ex">
                                {% if document.ex != 'NO' %}
                                    <span style="color: red;font-weight: 500">{{ document.ex }}</span>
                                {% else %}
                                    {{ document.ex }}
                                {% endif %}
                            </td>
                            <td class="weight" >{{ document.weight }}</td>
                            <td class="weightUnit" >{{ document.weightUnit }}</td>
                            <td class="platingSurface" >{{ document.platingSurface }}</td>
                            <td class="platingSurfaceUnit" >{{ document.platingSurfaceUnit }}</td>
                            <td class="material" >
                                {% if document.materials|length > 0 %}
                                    {% for material in document.materials %}
                                        <span class="badge bg-secondary me-1">{{ material.reference }}</span>
                                    {% endfor %}
                                {% else %}
                                    {{ document.material }}
                                {% endif %}
                            </td>
                            <td class="eccn" >{{ document.eccn }}</td>
                            <td class="rdo" >{{ document.rdo }}</td>
                            <td class="hts" >{{ document.hts }}</td>
                            <td class="comments" >
                                {% if document.commentaires|length > 0 %}
                                    <i class="fas fa-file-alt"
                                    style="color: #009BFF; cursor: pointer;"
                                    data-bs-toggle="tooltip"
                                    data-document-id="{{ document.id }}"
                                    onmouseenter="loadCommentsTooltip(this)"
                                    onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                    title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                    txt="{% for comment in document.commentaires %}
                                    <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                    par <em>{{ comment.user|e }}</em><br>
                                    {% endfor %}"
                                    ></i>
                                {% else %}
                                    <i class="fas fa-file-alt"
                                    style="color: #ccc; cursor: pointer;"
                                    title="Aucun commentaire"
                                    data-bs-toggle="tooltip"
                                    data-document-id="{{ document.id }}"
                                    onmouseenter="loadCommentsTooltip(this)"
                                    onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                    title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                    txt="{% for comment in document.commentaires %}
                                    <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                    par <em>{{ comment.user|e }}</em><br>
                                    {% endfor %}"
                                    ></i>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="true">
    <div class="modal-dialog" role="document" style="max-width: 90%;!important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal">Aletiq Package Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0" style='height: 80vh;overflow-y: auto;'>
                <table id="table_json" style="border-collapse: collapse;white-space: nowrap;" class="table table-hover m-0">
                    <thead>
                        <tr>
                            <th class="text-center" style=" padding: 8px;">Check</th>
                            <th class="text-center" style=" padding: 8px;">Level</th>
                            <th class="text-center" style=" padding: 8px;">ID_Aletiq</th>
                            <th class="text-center" style=" padding: 8px;">Reference</th>
                            <th class="text-center" style=" padding: 8px;">Revision Reference</th>
                            <th class="text-center" style=" padding: 8px;">Title</th>
                            <th class="text-center" style=" padding: 8px;">Catalogue Number</th>
                            <th class="text-center" style=" padding: 8px;">Material Type</th>
                            <th class="text-center" style=" padding: 8px;">Prod Drawing</th>
                            <th class="text-center" style=" padding: 8px;">Prod Draw Rev</th>
                            <th class="text-center" style=" padding: 8px;">Ex</th>
                            <th class="text-center" style=" padding: 8px;">RDO</th>
                        </tr>
                    </thead>
                    <tbody id="table_body"></tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary active" data-bs-dismiss="modal" id="validate_modal" >
                    <i class="fas fa-check"></i>
                    Valider
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_release" tabindex="-1" role="dialog" aria-labelledby="modal_release" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal_release">Release Package</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" >
                <div class="row">
                    <div class="d-flex align-items-center gap-4">
                        <label class="fw-500 form-label-sm" for="verif">Vérification</label>
                        <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="verif" name="verif" required data-live-search="true" data-size="10">
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="release_package" >Release</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal des commentaires -->
<div class="modal fade" id="modalComments" tabindex="-1" aria-labelledby="modalCommentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalCommentsLabel">
                    <i class="fas fa-comments me-2"></i>
                    Commentaires - <span id="documentReference"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="commentsModalBody">
                <!-- Les commentaires seront chargés ici -->
            </div>
            <div class="modal-footer custom-modal-footer">
                <div class="w-100">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <span class="badge bg-primary" style="background-color: #009BFF !important; font-size: 0.9rem; padding: 8px 12px;">
                                Package
                            </span>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control form-control-sm"
                                      id="newCommentText"
                                      placeholder="Ajouter un commentaire pour ce document..."
                                      rows="2"></textarea>
                        </div>
                        <div class="col-md-2">
                            <button type="button"
                                    class="btn btn-primary btn-sm w-100"
                                    id="addCommentBtn"
                                    onclick="addNewComment()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Variable globale pour stocker les données du document trouvé
    let foundDocumentData = null;

    // Variable globale pour stocker l'ID du document actuellement chargé dans le formulaire
    let currentFormDocumentId = null;

    // Fonction pour afficher le message d'alerte
    function showCharLimitAlert(fieldName) {
        const alertDiv = $('#charLimitAlert');
        const alertMessage = $('#alertMessage');

        alertMessage.text(`Limite maximale de 40 caractères atteinte pour le champ ${fieldName}`);
        alertDiv.removeClass('show').addClass('show').show();

        // Masquer automatiquement après 3 secondes
        setTimeout(function() {
            alertDiv.removeClass('show');
            setTimeout(function() {
                alertDiv.hide();
            }, 150);
        }, 3000);
    }

    // Fonction pour mettre à jour le compteur de caractères pour Alias
    function updateCharCount() {
        const aliasInput = document.getElementById('alias');
        const charCountElement = document.getElementById('charCount');
        const currentLength = aliasInput.value.length;

        // Limiter à 40 caractères et afficher alerte si nécessaire
        if (currentLength > 40) {
            aliasInput.value = aliasInput.value.slice(0, 40);
            showCharLimitAlert('Alias');
        } else if (currentLength === 40) {
            // Afficher le message quand on atteint exactement 40 caractères
            showCharLimitAlert('Alias');
        }

        charCountElement.textContent = Math.min(currentLength, 40) + '/40';
    }

    // Fonction pour mettre à jour le compteur de caractères pour Title
    function updateTitleCharCount() {
        const titleInput = document.getElementById('refTitleFra');
        const charCountElement = document.getElementById('titleCharCount');
        const currentLength = titleInput.value.length;

        // Limiter à 40 caractères et afficher alerte si nécessaire
        if (currentLength > 40) {
            titleInput.value = titleInput.value.slice(0, 40);
            showCharLimitAlert('Title');
        } else if (currentLength === 40) {
            // Afficher le message quand on atteint exactement 40 caractères
            showCharLimitAlert('Title');
        }

        charCountElement.textContent = Math.min(currentLength, 40) + '/40';
    }

    // Attacher les événements aux champs
    $(document).ready(function() {
        const aliasInput = $('#alias');
        const titleInput = $('#refTitleFra');

        // Événements de saisie pour Alias
        aliasInput.on('input keyup paste', updateCharCount);

        // Événements de saisie pour Title
        titleInput.on('input keyup paste', updateTitleCharCount);

        // Observer les changements de valeur (pour les modifications programmatiques)
        const aliasObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    updateCharCount();
                }
            });
        });

        const titleObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    updateTitleCharCount();
                }
            });
        });

        // Observer les changements d'attributs
        if (aliasInput[0]) {
            aliasObserver.observe(aliasInput[0], {
                attributes: true,
                attributeFilter: ['value']
            });
        }

        if (titleInput[0]) {
            titleObserver.observe(titleInput[0], {
                attributes: true,
                attributeFilter: ['value']
            });
        }

        // Utiliser un setInterval pour vérifier périodiquement les changements
        setInterval(function() {
            // Vérification pour Alias
            const currentAliasValue = aliasInput.val();
            const displayedAliasCount = $('#charCount').text();
            const expectedAliasCount = currentAliasValue.length + '/40';

            if (displayedAliasCount !== expectedAliasCount) {
                updateCharCount();
            }

            // Vérification pour Title
            const currentTitleValue = titleInput.val();
            const displayedTitleCount = $('#titleCharCount').text();
            const expectedTitleCount = currentTitleValue.length + '/40';

            if (displayedTitleCount !== expectedTitleCount) {
                updateTitleCharCount();
            }
        }, 100);

        // Mise à jour initiale
        updateCharCount();
        updateTitleCharCount();
    });

    $('#dmo').selectpicker('val', [{% for dmo in package.dmos %}'{{ dmo.id }}',{% endfor %}]);
    $('#activity').selectpicker('val', '{{ package.activity }}');
    $('#ex-package').selectpicker('val', '{{ package.ex }}');

    function handleDMOSelection($select) {
        const selectedOptions = $select.find('option:selected');

        if (selectedOptions.length > 0) {
            // Désactive le select project et ajoute l’option du premier DMO sélectionné
            $('#project').empty();
            $('#project').prop('disabled', true);
            $('#project').append('<option value="' + selectedOptions.first().data('project') + '">' + selectedOptions.first().data('project') + '</option>');
            $('#project').selectpicker('destroy');
            $('#project').selectpicker();

            // Désactive les options DMO qui ne correspondent pas au projet du premier DMO sélectionné
            const selectedProject = selectedOptions.first().data('project');
            $select.find('option').each(function() {
                const isMatchingProject = $(this).data('project') === selectedProject;
                $(this).prop('disabled', !isMatchingProject);
            });
        } else {
            // Réactive le select project et recharge toutes les options via Twig
            $('#project').prop('disabled', false);
            $('#project').empty();
            $('#project').selectpicker('destroy');
            {% for project in projects %}
                {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                    $('#project').append('<option value="{{ project.id }}" selected>{{ project.otp }}</option>');
                {% else %}
                    $('#project').append('<option value="{{ project.id }}">{{ project.otp }}</option>');
                {% endif %}
            {% endfor %}
            $('#project').selectpicker();

            // Réactive toutes les options du select DMO
            $select.find('option').each(function() {
                $(this).prop('disabled', false);
            });
        }
        // Réinitialise le select DMO pour refléter les changements
        $select.selectpicker('destroy');
        $select.selectpicker();
        $select.selectpicker('toggle');
    }

    $(document).on('change', '#dmo', function() {
        handleDMOSelection($(this));
    });

    handleDMOSelection($('#dmo'));
    $('#dmo').selectpicker('toggle');

    function updatePackage(){
        // D'abord sauvegarder le document actuel s'il y en a un sélectionné
        const documentId = $('#document_id').val();

        let documentPromise = Promise.resolve();

        if (documentId) {
            // Valider les données du document (y compris FMTS)
            if (!validate_document_data()) {
                return; // Arrêter si la validation échoue
            }

            // Sauvegarder le document actuel (silencieusement)
            documentPromise = maj_document(true);
        }

        // Ensuite sauvegarder le commentaire et mettre à jour le package
        documentPromise.then(() => {
            return saveCommentIfExists();
        }).then(() => {
            let id = {{ package.id }};
            let owner = $('#owner').text().trim().split(' ')[1];
            let activity = $('#activity').val();
            let ex = $('#ex-package').val();
            let dmo = $('#dmo').val();
            let project = $('#project').val();
            let description = $('#description').val();

            $.ajax({
                url: "{{ path('edit_package', {'id': package.id}) }}",
                type: 'POST',
                data: {
                    owner: owner,
                    activity: activity,
                    ex: ex,
                    dmo: dmo,
                    project: project,
                    description: description
                },
                success: function(response){
                    Toast.fire({
                        icon: 'success',
                        title: 'Package mise à jour !'
                    });
                },
                error: function(response){
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors de la mise à jour !'
                    });
                }
            });
        }).catch((error) => {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors de la sauvegarde du commentaire !'
            });
        });
    }

    $('#update').click(function(){
        updatePackage();
    });

    function releasePackage(){
        // D'abord sauvegarder le document actuel s'il y en a un sélectionné
        const documentId = $('#document_id').val();

        let documentPromise = Promise.resolve();

        if (documentId) {
            // Valider les données du document (y compris FMTS)
            if (!validate_document_data()) {
                return; // Arrêter si la validation échoue
            }

            // Sauvegarder le document actuel (silencieusement)
            documentPromise = maj_document(true);
        }

        // Ensuite sauvegarder le commentaire et faire la release
        documentPromise.then(() => {
            return saveCommentIfExists();
        }).then(() => {
            let id = {{ package.id }};
            $.ajax({
                url: "{{ path('release_package', {'id': package.id}) }}",
                type: 'POST',
                data: {
                    verif: $('#verif').val()
                },
                success: function(response){
                    Toast.fire({
                        icon: 'success',
                        title: 'Release lancée !'
                    });
                    window.location.href = "{{ path('app_package') }}#onlget=BE_1";
                },
                error: function(response){
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors du lancement de la release !'
                    });
                }
            });
        }).catch((error) => {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors de la sauvegarde du commentaire !'
            });
        });
    }

    // Fonction pour mettre à jour le package sans sauvegarder les commentaires
    function updatePackageOnly(){
        let id = {{ package.id }};
        let owner = $('#owner').text().trim().split(' ')[1];
        let activity = $('#activity').val();
        let ex = $('#ex-package').val();
        let dmo = $('#dmo').val();
        let project = $('#project').val();
        let description = $('#description').val();

        $.ajax({
            url: "{{ path('edit_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                owner: owner,
                activity: activity,
                ex: ex,
                dmo: dmo,
                project: project,
                description: description
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Package mise à jour !'
                });
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour !'
                });
            }
        });
    }

    // Fonction pour faire la release sans sauvegarder les commentaires
    function releasePackageOnly(){
        let id = {{ package.id }};
        $.ajax({
            url: "{{ path('release_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                verif: $('#verif').val()
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Release lancée !'
                });
                window.location.href = "{{ path('app_package') }}#onlget=BE_1";
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du lancement de la release !'
                });
            }
        });
    }

    function are_u_sure(){
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, lancer la release !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                // Appeler notre fonction releasePackage qui fait la validation et la sauvegarde
                releasePackage();
            }
        });
    }

    $('#release_package').click(function(){
        are_u_sure();
    });

    $('#release').click(function(){
        // si le table contien des lignes
        if($('#table-documents tbody tr').length > 0){
            $('#modal_release').modal('show');
        }else{
            Toast.fire({
                icon: 'error',
                title: 'Aucun document à release !'
            });
        }
    });

    let Q1 = [
        "a circuit board",
        "a clip, circlips, spring",
        "a complete connector (finished product)",
        "a glass-to-metal assembly or ceramic penetrator",
        "a nut",
        "a packaging item",
        "a protective cap (transportation, dust ...)",
        "a raw material",
        "a screw",
        "a shrinkable sleeve (cold, heat etc...)",
        "a sub-assembly of a connector",
        "a washer",
        "an elastomeric cable gland/boot/wiping membrane in FKM or silicon-based material",
        "an elastomeric seal",
        "an electrical wire or a cable with or without connexion parts",
        "an insulator",
        "an optical fiber with or without connexion parts",
        "an other manufactured single component",
    ];

    let Q2_LABEL = {
        "a circuit board": "Type of circuit",
        "a clip, circlips, spring": "Material it is made of:",
        "a complete connector (finished product)": "Is the voltage above 1000V (Uph-ph)",
        "a glass-to-metal assembly or ceramic penetrator": "Is the voltage above 1000V (Uph-ph)",
        "a nut": "Material it is made of:",
        "a packaging item": "What type of component:",
        "a protective cap (transportation, dust ...)": "",
        "a raw material": "Material it is made of:",
        "a screw": "Material it is made of:",
        "a shrinkable sleeve (cold, heat etc...)": "",
        "a sub-assembly of a connector": "Falling into one the category:",
        "a washer": "Washer type & material it is made of:",
        "an elastomeric cable gland/boot/wiping membrane in FKM or silicon-based material": "",
        "an elastomeric seal": "",
        "an electrical wire or a cable with or without connexion parts": "Which type of wire:",
        "an insulator": "Material it is made of:",
        "an optical fiber with or without connexion parts": "Which type of wire/fiber:",
        "an other manufactured single component": "Material it is made of:",
    };

    let Q3_LABEL = {
        "Aluminum and related alloy": "Format of the delivered material",
        "Connector with no contact": "Please pick the question above corresponding to the material of the main component",
        "Copper and related alloy": "Format of the delivered material",
        "NO": "What type of connector",
        "Stainless steel and related alloy": "Format of the delivered material",
    };

    let COMBINAISONS = [
        {Q1: "a circuit board", Q2: "Multi layer", Q3: "", HTS: "8534001100"},
        {Q1: "a circuit board", Q2: "Other", Q3: "", HTS: "8534001900"},
        {Q1: "a circuit board", Q2: "with passive components", Q3: "", HTS: "8534001900"},
        {Q1: "a clip, circlips, spring", Q2: "Copper (and alloy)", Q3: "", HTS: "7419803000"},
        {Q1: "a clip, circlips, spring", Q2: "Plastic", Q3: "", HTS: "3926909790"},
        {Q1: "a clip, circlips, spring", Q2: "Stainless Steel", Q3: "", HTS: "7320909090"},
        {Q1: "a complete connector (finished product)", Q2: "NO", Q3: "a parking, dummy connector (with no contact)", HTS: "8538909999"},
        {Q1: "a complete connector (finished product)", Q2: "NO", Q3: "A signal connector with electrical contacts", HTS: "856699099"},
        {Q1: "a complete connector (finished product)", Q2: "NO", Q3: "an optical connector", HTS: "8536700010"},
        {Q1: "a complete connector (finished product)", Q2: "YES", Q3: "", HTS: "8535900089"},
        {Q1: "a glass-to-metal assembly or ceramic penetrator", Q2: "NO", Q3: "", HTS: ""},
        {Q1: "a glass-to-metal assembly or ceramic penetrator", Q2: "YES", Q3: "", HTS: "8535900089"},
        {Q1: "a nut", Q2: "Aluminium", Q3: "", HTS: "7616100099"},
        {Q1: "a nut", Q2: "Copper", Q3: "", HTS: "7415330000"},
        {Q1: "a nut", Q2: "Stainless Steel", Q3: "", HTS: "7318163190"},
        {Q1: "a packaging item", Q2: "a transportation case or its foams", Q3: "", HTS: "3923900000"},
        {Q1: "a packaging item", Q2: "cardboard, kraft paper, bubble wrap etc…", Q3: "", HTS: "4808900000"},
        {Q1: "a protective cap (transportation, dust ...)", Q2: "", Q3: "", HTS: "3923509000"},
        {Q1: "a raw material", Q2: "Aluminum and related alloy", Q3: "bar, rod etc…", HTS: "7604291090"},
        {Q1: "a raw material", Q2: "Aluminum and related alloy", Q3: "forged, profil", HTS: "7604299090"},
        {Q1: "a raw material", Q2: "Copper and related alloy", Q3: "band", HTS: "7410120000"},
        {Q1: "a raw material", Q2: "Copper and related alloy", Q3: "bar, rod etc…", HTS: "7407211000"},
        {Q1: "a raw material", Q2: "Copper and related alloy", Q3: "profile", HTS: "7407219000"},
        {Q1: "a raw material", Q2: "Stainless steel and related alloy", Q3: "bar, rod etc…", HTS: "7222309790"},
        {Q1: "a raw material", Q2: "Stainless steel and related alloy", Q3: "forged, profil", HTS: "7222309090"},
        {Q1: "a screw", Q2: "Aluminium", Q3: "", HTS: "7616100099"},
        {Q1: "a screw", Q2: "Copper", Q3: "", HTS: "7415330000"},
        {Q1: "a screw", Q2: "Stainless Steel", Q3: "", HTS: "7318163190"},
        {Q1: "a shrinkable sleeve (cold, heat etc...)", Q2: "", Q3: "", HTS: "3917320090"},
        {Q1: "a sub-assembly of a connector", Q2: "Connector with no contact", Q3: "Please pick the question above corresponding to the material of the main component", HTS: "-"},
        {Q1: "a sub-assembly of a connector", Q2: "Insulator subassembly", Q3: "", HTS: "8538909999"},
        {Q1: "a sub-assembly of a connector", Q2: "Main body and mobile parts (coupling ring, cable gland etc..)", Q3: "", HTS: "8538909999"},
        {Q1: "a sub-assembly of a connector", Q2: "Parking, subassembly, metalic protective cap etc…", Q3: "", HTS: "8538909999"},
        {Q1: "a washer", Q2: "Copper", Q3: "", HTS: "7415210000"},
        {Q1: "a washer", Q2: "Flat Washer - Stainless Steel", Q3: "", HTS: "7318220091"},
        {Q1: "a washer", Q2: "Spring washer - Stainless Steel", Q3: "", HTS: "7318210091"},
        {Q1: "an elastomeric cable gland/boot/wiping membrane in FKM or silicon-based material", Q2: "", Q3: "", HTS: "926909790"},
        {Q1: "an elastomeric seal", Q2: "", Q3: "", HTS: ""},
        {Q1: "an electrical wire or a cable with or without connexion parts", Q2: "Coaxial wire/harness", Q3: "", HTS: "8544200090"},
        {Q1: "an electrical wire or a cable with or without connexion parts", Q2: "Hybrid (Coaxial + conductors)", Q3: "", HTS: "8544429090"},
        {Q1: "an electrical wire or a cable with or without connexion parts", Q2: "Single/mutiple conductor wire/harness", Q3: "", HTS: "8544429090"},
        {Q1: "an insulator", Q2: "Ceramic", Q3: "", HTS: "8547100000"},
        {Q1: "an insulator", Q2: "Glass (bid of glass)", Q3: "", HTS: "7020008000"},
        {Q1: "an insulator", Q2: "PEEK/PEK/POM", Q3: "", HTS: "8547200090"},
        {Q1: "an insulator", Q2: "Silicon", Q3: "", HTS: "926909790"},
        {Q1: "an optical fiber with or without connexion parts", Q2: "Optical fiber/harness", Q3: "", HTS: "8544700090"},
        {Q1: "an other manufactured single component", Q2: "Aluminum and related alloy", Q3: "", HTS: "7616999099"},
        {Q1: "an other manufactured single component", Q2: "Copper and related alloy", Q3: "", HTS: "7419809099"},
        {Q1: "an other manufactured single component", Q2: "Nickel Alloy", Q3: "", HTS: "7508900000"},
        {Q1: "an other manufactured single component", Q2: "Plastic/Composite", Q3: "", HTS: "3926909790"},
        {Q1: "an other manufactured single component", Q2: "Silver", Q3: "", HTS: "7106920000"},
        {Q1: "an other manufactured single component", Q2: "Steel or stainless steel", Q3: "", HTS: "7326909890"},
        {Q1: "an other manufactured single component", Q2: "Titanium and related alloy", Q3: "", HTS: "8108909099"}
    ];

    function populateSelect(select, data) {
        select.selectpicker('destroy').empty();
        if (data.length) {
            select.append('<option value=""></option>');
            data.forEach(item => item && select.append(`<option value="${item}">${item}</option>`));
            select.selectpicker();
            $('#' + select.attr('id') + '_DIV').show();
        }
        select.selectpicker();
    }

    function getFilteredValues(Q1, Q2 = null, Q3 = null, key) {
        return [...new Set(COMBINAISONS.filter(item => item.Q1 === Q1 && (Q2 === null || item.Q2 === Q2) && (Q3 === null || item.Q3 === Q3) && item[key] !== "").map(item => item[key]))];
    }

    function updateHTS() {
        $('#hts').val(getFilteredValues($('#Q1').val(), $('#Q2').val(), $('#Q3').val(), 'HTS')[0]);
    }

    populateSelect($('#Q1'), Q1);

    $('#Q1').change(function () {
        let Q1 = $(this).val();
        $('#Q2_DIV').hide();
        $('#Q3_DIV').hide();
        $('#hts').val('');
        $('#Q2_LABEL').text(Q2_LABEL[Q1]);
        populateSelect($('#Q2'), getFilteredValues(Q1, null, null, 'Q2'));
        populateSelect($('#Q3'), []);
        updateHTS();
    });

    $('#Q2').change(function () {
        let Q1 = $('#Q1').val();
        let Q2 = $(this).val();
        $('#Q3_DIV').hide();
        $('#hts').val('');
        $('#Q3_LABEL').text(Q3_LABEL[Q2]);
        populateSelect($('#Q3'), getFilteredValues(Q1, Q2, null, 'Q3'));
        updateHTS();
    });

    $('#Q3').change(updateHTS);




    // ECCN / RDO
    let rdo_combination = [
        {rdo: '0578', activity: 'ENERGY_SIGNAL', eccn: 'NOCLASS'},
        {rdo: '0578', activity: 'ENERGY_POWER', eccn: 'NOCLASS'},
        {rdo: '1229', activity: 'ENERGY_SIGNAL', eccn: '8A002.c'},
        {rdo: '0580', activity: 'MOB_AERO', eccn: 'NOCLASS'},
        {rdo: '0581', activity: 'MOB_AERO', eccn: 'TBD'},
        {rdo: '0579', activity: 'MOB_INDUS', eccn: 'NOCLASS'},
        {rdo: '1226', activity: 'MOB_INDUS', eccn: 'TBD'},
        {rdo: '0826', activity: 'METHOD', eccn: 'NOCLASS'},
        {rdo: '1229', activity: 'ENERGY_POWER', eccn: '8A002.c'},
        {rdo: '1229', activity: 'ENERGY_POWER', eccn: 'TBD'},
        {rdo: '1229', activity: 'ENERGY_SIGNAL', eccn: 'TBD'},
        {rdo: '0578', activity: 'ENERGY_RENEW', eccn: 'NOCLASS'},
        {rdo: '1229', activity: 'ENERGY_RENEW', eccn: '8A002.c'},
        {rdo: '1229', activity: 'ENERGY_RENEW', eccn: 'TBD'},
        {rdo: '1229', activity: 'METHOD', eccn: 'TBD'},
        {rdo: '0826', activity: 'OSI', eccn: 'NOCLASS'},
        {rdo: '1229', activity: 'OSI', eccn: 'TBD'},
        {rdo: '1229', activity: 'METHOD', eccn: '8A002.c'},
        {rdo: '1229', activity: 'OSI', eccn: '8A002.c'},
        {rdo: '0581', activity: 'MOB_AERO', eccn: '8A002.c'},
        {rdo: '1226', activity: 'MOB_INDUS', eccn: '8A002.c'},
    ];

    let eccn_combination = [
        {Q1: 'Is or includes an optical penetration', Q2_title: 'What is the market of the finished product', Q2: 'Military market only', eccn: 'TBD'},
        {Q1: 'Is or includes an optical penetration', Q2_title: 'What is the market of the finished product', Q2: 'Civil market only', eccn: 'NOCLASS'},
        {Q1: 'Is or includes an optical penetration', Q2_title: 'What is the market of the finished product', Q2: 'Both military and civil markets', eccn: '8A002.c'},
        {Q1: 'A dual use product', Q2_title: '', Q2: '', eccn: '8A002.c'},
        {Q1: 'A military product', Q2_title: '', Q2: '', eccn: 'TBD'},
        {Q1: 'None of the above', Q2_title: '', Q2: '', eccn: 'NOCLASS'}
    ];


    populateSelect($('#Q1_eccn'), [...new Set(eccn_combination.map(item => item.Q1))]);

    function getFilteredValuesECCN(Q1, Q2=null, key) {
        return [...new Set(eccn_combination.filter(item => item.Q1 === Q1 && (Q2 === null || item.Q2 === Q2) && item[key] !== "").map(item => item[key]))];
    }

    $('#Q1_eccn').change(function () {
        let Q1 = $(this).val();
        $('#Q2_eccn_DIV').hide();
        $('#eccn').val('');
        updateRDO();
        if (Q1) {
            $('#Q2_eccn_LABEL').text(eccn_combination.find(item => item.Q1 === Q1).Q2_title);
        }
        populateSelect($('#Q2_eccn'), getFilteredValuesECCN(Q1, null, 'Q2'));
        updateECCN();
    });

    $('#Q2_eccn').change(updateECCN);



    function updateECCN() {
        $('#eccn').val(getFilteredValuesECCN($('#Q1_eccn').val(), $('#Q2_eccn').val(), 'eccn'));
        updateRDO();
    }

    $('#activity').change(updateRDO);

    function updateRDO() {
        let activity = $('#activity').val();
        let eccn = $('#eccn').val();
        if (!activity || !eccn) {
            $('#rdo').val('');
        } else {
            let rdo = rdo_combination.find(item => item.activity === activity && item.eccn === eccn);
            $('#rdo').val(rdo ? rdo.rdo : '');
        }
    }


    let prodDraw = "";
    let prodDrawRev = "";

    function refreshIframe() {
        if ((prodDraw!==$('#prodDraw').val() || prodDrawRev!==$('#prodDrawRev').val()) && $('#prodDraw').val() && $('#prodDrawRev').val()) {
            prodDraw = $('#prodDraw').val();
            prodDrawRev = $('#prodDrawRev').val();
            $('#aletiq_preview').attr('src', "https://app.aletiq.com/parts/preview/id/"+prodDraw+"/revision/"+prodDrawRev);
        }
        else {
            prodDraw = "";
            prodDrawRev = "";
            $('#aletiq_preview').attr('src', "");
        }
    }

    $('#prodDraw').change(refreshIframe);
    $('#prodDrawRev').change(refreshIframe);

    $(function () {
        $('[data-bs-toggle="tooltip"]').tooltip()
    })

    function validate_document_data() {
        const inputList = [
            'reference', 'refRev', 'idAletiq', 'prodDraw', 'prodDrawRev', 'alias', 'refTitleFra', 'action',
            'docType', 'materialType', 'inventoryImpact', 'ex', 'custDrawing', 'custDrawingRev', 'weight',
            'weightUnit', 'platingSurface', 'platingSurfaceUnit', 'eccn', 'rdo', 'hts', 'comments'
        ];

        // Vérifier si un matériau sélectionné contient "FMTS"
        let hasFMTSMaterial = false;

        // Méthode 1 : Vérifier dans les badges visibles
        $('.material-tag').each(function() {
            const materialText = $(this).text().trim();
            // Enlever le bouton de fermeture du texte
            const materialReference = materialText.replace(/×$/, '').trim();
            if (materialReference.includes('FMTS')) {
                hasFMTSMaterial = true;
                return false; // Sortir de la boucle
            }
        });

        // Méthode 2 : Vérifier via l'instance MaterialTags
        if (!hasFMTSMaterial && window.materialTagsInstance) {
            const selectedMaterials = Array.from(window.materialTagsInstance.materials.values());
            selectedMaterials.forEach(material => {
                if (material.reference && material.reference.includes('FMTS')) {
                    hasFMTSMaterial = true;
                }
            });
        }

        let isValid = true;

        inputList.forEach(id => {
            const $input = $('#' + id);
            let isRequired = $input.prop('required');
            const isEmpty = !$input.val() || $input.val().trim() === "";
            const isSelect = $input.hasClass('selectpicker');
            const $target = isSelect ? $input.next() : $input;

            // Rendre Plating surface et Plating surface unit obligatoires si matériau FMTS
            if (hasFMTSMaterial && (id === 'platingSurface' || id === 'platingSurfaceUnit')) {
                isRequired = true;
            }

            if (isRequired && isEmpty) {
                let errorMessage = 'Veuillez remplir tous les champs obligatoires !';
                if (hasFMTSMaterial && (id === 'platingSurface' || id === 'platingSurfaceUnit')) {
                    errorMessage = 'Les champs Plating Surface sont obligatoires pour les matériaux FMTS !';
                }

                Toast.fire({ icon: 'error', title: errorMessage });
                $target.addClass('border-danger');
                isValid = false;
            } else {
                $target.removeClass('border-danger');
            }
        });

        return isValid;
    }

    function remove_border_red() {
        $('input, select').each(function() {
            if ($(this).hasClass('selectpicker')) {
                $(this).next().removeClass('border-danger');
            } else {
                $(this).removeClass('border-danger');
            }
        });
    }


    function collect_data_document() {
        let reference = $('#reference').val();
        let refRev = $('#refRev').val();
        let idAletiq = $('#idAletiq').val();
        // Convertir idAletiq en entier ou null
        if (idAletiq && idAletiq.trim() !== '') {
            idAletiq = parseInt(idAletiq);
        } else {
            idAletiq = null;
        }
        let prodDraw = $('#prodDraw').val();
        let prodDrawRev = $('#prodDrawRev').val();
        let alias = $('#alias').val();
        let refTitleFra = $('#refTitleFra').val();
        let action = $('#action').val();
        let docType = $('#docType').val();
        let materialType = $('#materialType').val();
        let inventoryImpact = $('#inventoryImpact').val();
        let ex = $('#ex').val();
        let custDrawing = $('#custDrawing').val();
        let custDrawingRev = $('#custDrawingRev').val();
        let weight = $('#weight').val();
        let weightUnit = $('#weightUnit').val();
        let materials = [];
        // Récupérer les matériaux sélectionnés
        $('#materials-container input[name="materials[]"]').each(function() {
            materials.push($(this).val());
        });
        let platingSurface = $('#platingSurface').val();
        let platingSurfaceUnit = $('#platingSurfaceUnit').val();
        let internalMachRec = $('#internalMachRec').is(':checked') ? 1 : 0;
        let eccn = $('#eccn').val();
        let rdo = $('#rdo').val();
        let hts = $('#hts').val();
        let comments = $('#comments').val();

        // Champs supplémentaires - Convertir les champs numériques
        let cls = null;
        if ($('#cls').length && $('#cls').val() && $('#cls').val().trim() !== '') {
            cls = parseInt($('#cls').val());
        }

        let moq = null;
        if ($('#moq').length && $('#moq').val() && $('#moq').val().trim() !== '') {
            moq = parseInt($('#moq').val());
        }
        let productCode = $('#productCode').length ? $('#productCode').val() : null;
        let prodAgent = $('#prodAgent').length ? $('#prodAgent').val() : null;
        let mof = $('#mof').length ? $('#mof').val() : null;
        let commodityCode = $('#commodityCode').length ? $('#commodityCode').val() : null;
        let purchasingGroup = $('#purchasingGroup').length ? $('#purchasingGroup').val() : null;
        let matProdType = $('#matProdType').length ? $('#matProdType').val() : null;
        let unit = $('#Unit').length ? $('#Unit').val() : null;
        let leadtime = null;
        if ($('#leadtime').length && $('#leadtime').val() && $('#leadtime').val().trim() !== '') {
            leadtime = parseInt($('#leadtime').val());
        }

        let prisDans1 = null;
        if ($('#prisDans1').length && $('#prisDans1').val() && $('#prisDans1').val().trim() !== '') {
            prisDans1 = parseFloat($('#prisDans1').val());
        }

        let prisDans2 = null;
        if ($('#prisDans2').length && $('#prisDans2').val() && $('#prisDans2').val().trim() !== '') {
            prisDans2 = parseFloat($('#prisDans2').val());
        }
        let fia = $('#fia').length ? $('#fia').val() : null;
        let metroTime = null;
        if ($('#metroTime').length && $('#metroTime').val() && $('#metroTime').val().trim() !== '') {
            metroTime = parseFloat($('#metroTime').val());
        }
        let procType = $('#procType').length ? $('#procType').val() : null;

        // Champs de type tableau - Convertir les chaînes séparées par des virgules en tableaux JSON
        let qInspection = null;
        if ($('#qInspection').length && $('#qInspection').val()) {
            qInspection = JSON.stringify($('#qInspection').val().split(',').map(item => item.trim()));
        }

        let qDynamization = $('#qDynamization').length ? $('#qDynamization').val() : null;

        let qDocRec = null;
        if ($('#qDocRec').length && $('#qDocRec').val()) {
            qDocRec = JSON.stringify($('#qDocRec').val().split(',').map(item => item.trim()));
        }

        let qControlRouting = $('#qControlRouting').length ? $('#qControlRouting').val() : null;
        let criticalComplete = $('#criticalComplete').length ? $('#criticalComplete').val() : null;

        // Champs de type booléen
        let switchAletiq = $('#switchAletiq').length ? $('#switchAletiq').is(':checked') ? 1 : 0 : null;

        let metroControl = null;
        if ($('#metroControl').length && $('#metroControl').val()) {
            metroControl = JSON.stringify($('#metroControl').val().split(',').map(item => item.trim()));
        }

        let docImpact = $('#docImpact').length ? $('#docImpact').is(':checked') ? 1 : 0 : 0;

        // Créer un objet avec les données du formulaire
        let formData = {
            reference: reference,
            refRev: refRev,
            idAletiq: idAletiq,
            prodDraw: prodDraw,
            prodDrawRev: prodDrawRev,
            alias: alias,
            refTitleFra: refTitleFra,
            action: action,
            docType: docType,
            materialType: materialType,
            inventoryImpact: inventoryImpact,
            ex: ex,
            custDrawing: custDrawing,
            custDrawingRev: custDrawingRev,
            weight: weight,
            weightUnit: weightUnit,
            materials: materials,
            platingSurface: platingSurface,
            platingSurfaceUnit: platingSurfaceUnit,
            internalMachRec: internalMachRec,
            eccn: eccn,
            rdo: rdo,
            hts: hts,
            comments: comments,

            // Champs supplémentaires
            cls: cls,
            moq: moq,
            productCode: productCode,
            prodAgent: prodAgent,
            mof: mof,
            commodityCode: commodityCode,
            purchasingGroup: purchasingGroup,
            matProdType: matProdType,
            Unit: unit,
            leadtime: leadtime,
            prisDans1: prisDans1,
            prisDans2: prisDans2,
            fia: fia,
            metroTime: metroTime,
            procType: procType,
            qInspection: qInspection,
            qDynamization: qDynamization,
            qDocRec: qDocRec,
            qControlRouting: qControlRouting,
            criticalComplete: criticalComplete,
            switchAletiq: switchAletiq,
            metroControl: metroControl,
            docImpact: docImpact,
        };

        // Si nous avons des données d'un document trouvé, les fusionner avec les données du formulaire
        if (foundDocumentData) {
            // Copier les champs qui ne sont pas dans le formulaire
            for (let key in foundDocumentData) {
                // Ne pas écraser les champs qui sont déjà dans le formulaire
                if (formData[key] === undefined || formData[key] === null || formData[key] === '') {
                    formData[key] = foundDocumentData[key];
                }
            }
        }

        return formData;
    }

    function empty_fields() {
        // Réinitialiser la variable globale
        foundDocumentData = null;

        // Champs de base
        $('#reference').val('');
        $('#refRev').val('');
        $('#idAletiq').val('');
        $('#prodDraw').val('');
        $('#prodDrawRev').val('');
        $('#alias').val('');
        updateCharCount(); // Mettre à jour le compteur après avoir vidé le champ
        $('#refTitleFra').val('');
        updateTitleCharCount(); // Mettre à jour le compteur après avoir vidé le champ
        $('#action').selectpicker('val', '');
        $('#docType').selectpicker('val', '');
        $('#internalMachRec').prop('checked', false);
        $('#materialType').selectpicker('val', '');
        $('#inventoryImpact').selectpicker('val', '');
        $('#ex').selectpicker('val', '');
        $('#custDrawing').val('');
        $('#custDrawingRev').val('');
        $('#weight').val('');
        $('#weightUnit').selectpicker('val', '');
        // Vider les matériaux
        if (window.materialTagsInstance) {
            window.materialTagsInstance.clear();
        }
        $('#platingSurface').val('');
        $('#platingSurfaceUnit').selectpicker('val', '');
        $('#eccn').val('');
        $('#rdo').val('');
        $('#hts').val('');
        $('#comments').val('');

        // Champs supplémentaires
        if ($('#cls').length) $('#cls').val('');
        if ($('#moq').length) $('#moq').val('');
        if ($('#productCode').length) {
            if ($('#productCode').hasClass('selectpicker')) {
                $('#productCode').selectpicker('val', '');
            } else {
                $('#productCode').val('');
            }
        }
        if ($('#prodAgent').length) $('#prodAgent').val('');
        if ($('#mof').length) $('#mof').val('');
        if ($('#commodityCode').length) {
            if ($('#commodityCode').hasClass('selectpicker')) {
                $('#commodityCode').selectpicker('val', '');
            } else {
                $('#commodityCode').val('');
            }
        }
        if ($('#purchasingGroup').length) {
            if ($('#purchasingGroup').hasClass('selectpicker')) {
                $('#purchasingGroup').selectpicker('val', '');
            } else {
                $('#purchasingGroup').val('');
            }
        }
        if ($('#matProdType').length) {
            if ($('#matProdType').hasClass('selectpicker')) {
                $('#matProdType').selectpicker('val', '');
            } else {
                $('#matProdType').val('');
            }
        }
        if ($('#Unit').length) {
            if ($('#Unit').hasClass('selectpicker')) {
                $('#Unit').selectpicker('val', '');
            } else {
                $('#Unit').val('');
            }
        }
        if ($('#leadtime').length) $('#leadtime').val('');
        if ($('#prisDans1').length) $('#prisDans1').val('');
        if ($('#prisDans2').length) $('#prisDans2').val('');
        if ($('#fia').length) $('#fia').val('');
        if ($('#metroTime').length) $('#metroTime').val('');
        if ($('#procType').length) {
            if ($('#procType').hasClass('selectpicker')) {
                $('#procType').selectpicker('val', '');
            } else {
                $('#procType').val('');
            }
        }
        if ($('#qInspection').length) $('#qInspection').val('');
        if ($('#qDynamization').length) $('#qDynamization').val('');
        if ($('#qDocRec').length) $('#qDocRec').val('');
        if ($('#qControlRouting').length) $('#qControlRouting').val('');
        if ($('#criticalComplete').length) $('#criticalComplete').val('');
        if ($('#switchAletiq').length) $('#switchAletiq').prop('checked', false);
        if ($('#metroControl').length) $('#metroControl').val('');
        if ($('#docImpact').length) $('#docImpact').prop('checked', false);

        // Q1 / Q2 / Q3
        $('#Q1').selectpicker('val', '');
        $('#Q2').selectpicker('val', '');
        $('#Q3').selectpicker('val', '');
        $('#Q1').trigger('change');
        // ECCN / RDO
        $('#Q1_eccn').selectpicker('val', '');
        $('#Q2_eccn').selectpicker('val', '');
        $('#Q1_eccn').trigger('change');

        remove_border_red();
        refreshIframe();

        // Masquer le bouton des commentaires antérieurs
        updatePreviousCommentsButton(null);
    }

    function add_document(){
        let data = collect_data_document();
        let csv = $('#csv-file').prop('files')[0];
        if (!validate_document_data()) return;

        data = Object.fromEntries(Object.entries(data).filter(([key, value]) => value !== ""));

        $.ajax({
            url: "{{ path('add_document', {'id': package.id}) }}",
            type: 'POST',
            data: data,
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Document ajouté !'
                });
                empty_fields();
                refresh_table_documents();
                processCsvFile(csv);
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de l\'ajout du document !'
                });
            }
        });
    }

    $('#add-document').click(function(){
        add_document();
    });

    function maj_document(silent = false){
        let data = collect_data_document();
        let documentId = $('#document_id').val();



        if (!documentId) {
            if (!silent) {
                Toast.fire({
                    icon: 'error',
                    title: 'Aucun document sélectionné !'
                });
            }
            return Promise.reject('No document selected');
        }

        if (!validate_document_data()) return Promise.reject('Validation failed');

        // Filtrer seulement les chaînes vides, mais garder les null et les nombres
        data = Object.fromEntries(Object.entries(data).filter(([key, value]) => {
            return value !== "" && value !== undefined;
        }));


        return new Promise((resolve, reject) => {
            $.ajax({
                url: "{{ path('maj_document', {'id': 0}) }}".replace('0', documentId),
                type: 'POST',
                data: data,
                success: function(response){

                    if (!silent) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Document mis à jour !'
                        });
                        refresh_table_documents();
                    }
                    resolve(response);
                },
                error: function(xhr, status, error){


                    if (!silent) {
                        Toast.fire({
                            icon: 'error',
                            title: 'Erreur lors de la mise à jour du document ! (Code: ' + xhr.status + ')'
                        });
                    }
                    reject(xhr);
                }
            });
        });
    }

    // Ajouter un bouton de mise à jour ou l'associer à un événement existant
    $(document).on('click', '#update-document', function(){
        maj_document();
    });

    function change_border(input) {
        if (input.prop('required') && input.val() === "") {
            if (input.hasClass('selectpicker')) {
                input.next().addClass('border-danger');
            } else {
                input.addClass('border-danger');
            }
        } else {
            if (input.hasClass('selectpicker')) {
                input.next().removeClass('border-danger');
            } else {
                input.removeClass('border-danger');
            }
        }
    }

    $('input, select').change(function() {
        change_border($(this));
    });

    // Fonction pour valider la référence article selon le doc type
    function validateReferenceLength() {
        const docType = $('#docType').val();
        const reference = $('#reference').val();
        const referenceField = $('#reference');

        // Si le doc type n'est pas "doc" et qu'il y a une référence
        if (docType && docType.toLowerCase() !== 'doc' && reference) {
            if (reference.length !== 18) {
                // Afficher un toast d'avertissement
                Toast.fire({
                    icon: 'warning',
                    title: `Attention : La référence doit faire exactement 18 caractères pour le type "${docType}" (actuellement ${reference.length} caractères)`
                });

                // Ajouter un indicateur visuel sur le champ
                referenceField.addClass('border-warning');
                referenceField.attr('title', `Référence doit faire 18 caractères (actuellement ${reference.length})`);
            } else {
                // Supprimer l'indicateur si la référence est correcte
                referenceField.removeClass('border-warning');
                referenceField.removeAttr('title');
            }
        } else {
            // Supprimer l'indicateur si doc type = "doc" ou pas de référence
            referenceField.removeClass('border-warning');
            referenceField.removeAttr('title');
        }
    }

    // Attacher la validation aux changements de doc type et référence
    $('#docType').change(function() {
        validateReferenceLength();
    });

    $('#reference').on('input keyup paste', function() {
        // Délai pour permettre à la valeur de se mettre à jour
        setTimeout(validateReferenceLength, 100);
    });

    // Fonction pour sauvegarder un commentaire si il y en a un
    function saveCommentIfExists() {
        const commentText = $('#comments').val().trim();
        const documentId = $('#document_id').val();

        if (!commentText || !documentId) {
            return Promise.resolve(); // Pas de commentaire à sauvegarder
        }

        return new Promise((resolve, reject) => {
            $.ajax({
                url: "{{ path('app_commentaire_new') }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    documentId: documentId,
                    content: commentText,
                    type: 'principal',
                    state: 'package'
                }),
                success: function(response) {
                    // Vider le champ commentaire après sauvegarde
                    $('#comments').val('');
                    resolve(response);
                },
                error: function(xhr) {
                    reject(xhr);
                }
            });
        });
    }

    // Fonction pour sauvegarder les matériaux du document actuel
    function saveCurrentDocumentMaterials() {
        const documentId = $('#document_id').val();

        if (!documentId) {
            return Promise.resolve(); // Pas de document sélectionné
        }

        // Collecter les matériaux actuels
        let materials = [];
        $('#materials-container input[name="materials[]"]').each(function() {
            materials.push($(this).val());
        });

        return new Promise((resolve, reject) => {
            $.ajax({
                url: "/index.php/api/document/" + documentId + "/materials",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    materials: materials
                }),
                success: function(response) {
                    resolve(response);
                },
                error: function(xhr) {
                    reject(xhr);
                }
            });
        });
    }

    function delete_document(){
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Vous ne pourrez pas récupérer ce document!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-danger',
                cancelButton: 'btn btn-sm btn-secondary'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                let url = "{{ path('delete_document', {'id': 0}) }}";
                url = url.replace('0', $('#document_id').val());
                $.ajax({
                    url: url,
                    type: 'POST',
                    success: function(response){
                        Toast.fire({
                            icon: 'success',
                            title: 'Document supprimé !'
                        });
                        $('#add-document').show();
                        $('#delete-document').hide();
                        empty_fields();
                        refresh_table_documents();
                    },
                    error: function(response){
                        Toast.fire({
                            icon: 'error',
                            title: 'Erreur lors de la suppression du document !'
                        });
                    }
                });
            }
        });
    }

    $('#delete-document').click(function(){
        delete_document();
    });


    $('#csv-modal').on('click', function(){
        $('#modal').modal('show');
    });

    function processCsvFile(file) {
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvContent = e.target.result;
                const jsonData = csvToJson(csvContent);
                $('#csv-modal').text(jsonData.length + ' lignes');
                $('#csv-modal').show();
                fill_modal(jsonData);
            };
            reader.readAsText(file);
        }
    }

    $('#csv-file').change(function() {
        processCsvFile(this.files[0]);
    });

    function csvToJson(csv) {
        const lines = csv.trim().split("\n");
        const headers = lines[0].split(";");

        let references = [];
        $.ajax({
            url: "{{ path('get_references', {'id': package.id}) }}",
            type: 'POST',
            async: false,
            success: function(response){
                references = response;
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la récupération des références !'
                });
            }
        });

        const jsonData = lines.slice(1).map(line => {
            const values = line.split(";");
            return headers.reduce((obj, header, i) => {
            obj[header.trim()] = values[i]?.trim() || null;
            return obj;
            }, {});
        });

        jsonData.forEach(line => {
            if (references.includes(line.product_id)) {
            line.check = '<i class="fas fa-check"></i>';
            }
        });

        // Move lines with "check" set to the end of the array
        jsonData.sort((a, b) => parseInt(a.level) - parseInt(b.level));

        return jsonData;
    }

    function fill_modal(json){
        let table_body = $('#table_body');
        table_body.empty();
        json.forEach((line, i) => {
            if (line['rdo_1']) {
                line['rdo_1'] = line['rdo_1'].split('-')[0].trim();
            }
            let tr = $(`<tr class="fill_fields" id-row="${i}"></tr>`);
            tr.append(`<td class="text-center">${line['check'] || ''}</td>`);
            tr.append(`<td id="level_${i}" class="text-center">${line['level'] || ''}</td>`);
            tr.append(`<td id="product_internal_aletiq_id_${i}" class="text-center">${line['product_internal_aletiq_id'] || ''}</td>`);
            tr.append(`<td id="product_id_${i}" class="text-center">${line['product_id'] || ''}</td>`);
            tr.append(`<td id="product_definition_${i}" class="text-center">${line['product_definition'] || ''}</td>`);
            tr.append(`<td id="product_name_${i}" class="text-center">${line['product_name'] || ''}</td>`);
            tr.append(`<td id="catalogue_n__${i}" class="text-center">${line['catalogue_n_'] || ''}</td>`);
            tr.append(`<td id="type_d_article_${i}" class="text-center">${line['type_d_article'] || ''}</td>`);
            tr.append(`<td id="drawings_id_${i}" class="text-center">${line['drawings_id'] || ''}</td>`);
            tr.append(`<td id="drawings_revision_${i}" class="text-center">${line['drawings_revision'] || ''}</td>`);
            tr.append(`<td id="ex_${i}" class="text-center">${line['ex'] || ''}</td>`);
            tr.append(`<td id="rdo_1_${i}" class="text-center">${line['rdo_1'] || ''}</td>`);
            table_body.append(tr);
        });
    }

    function fill_fields(){
        empty_fields();
        let id_row = $(this).attr('id-row');
        $('#idAletiq').val($('#product_internal_aletiq_id_'+id_row).text());
        $('#reference').val($('#product_id_'+id_row).text());
        $('#refRev').val($('#product_definition_'+id_row).text());
        $('#refTitleFra').val($('#product_name_'+id_row).text());
        $('#alias').val($('#catalogue_n__'+id_row).text());
        var type_d_article = $('#type_d_article_'+id_row).text();
        console.log(type_d_article);
        switch (type_d_article) {
            case "Produits Semi-finis (HALB)":
                $('#materialType').selectpicker('val', "SEMI-FINISHED PRODUCT");
                break;
            case "Produits Finis (FERT)":
                console.log("FINISHED PRODUCT");
                $('#materialType').selectpicker('val', "FINISHED PRODUCT");
                break;
            case "Conditionnements (VERP)":
                $('#materialType').selectpicker('val', "PACKAGING");
                break;
            case "Matieres premieres (ROH)":
                $('#materialType').selectpicker('val', "NON VALUATED MATERIAL");
                break;
            default:
                $('#materialType').selectpicker('val', "");
        }
        $('#prodDraw').val($('#drawings_id_'+id_row).text());
        $('#prodDrawRev').val($('#drawings_revision_'+id_row).text());
        $('#ex').val($('#ex_'+id_row).text());
        $('#rdo').val($('#rdo_1_'+id_row).text());
        refreshIframe();
        $('#modal').modal('hide');
        remove_border_red();
    }

    $(document).on('click', '.fill_fields', fill_fields);


    function refresh_table_documents(){
        $.ajax({
            url: "{{ path('update_package', {'id': package.id}) }}",
            type: 'POST',
            success: function(response){
                $('#div-documents').html($(response).find('#div-documents').html());
                $(function () {
                    $('[data-bs-toggle="tooltip"]').tooltip()
                })
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour des documents !'
                });
            }
        });
    }


    function fill_fields_document(){
        let row = $(this);
        if (row.hasClass('selected-row')) {
            empty_fields();
            row.removeClass('selected-row');
            $('#add-document').show();
            $('#delete-document').hide();
            return;
        }
        let id = row.attr('id').split('_')[1];
        let reference = row.find('.reference').text();
        let refRev = row.find('.refRev').text();
        let idAletiq = row.find('.idAletiq').text();
        let prodDraw = row.find('.prodDraw').text();
        let prodDrawRev = row.find('.prodDrawRev').text();
        let alias = row.find('.alias').text();
        let refTitleFra = row.find('.refTitleFra').text();
        let action = row.find('.action').text();
        let docType = row.find('.docType').text();
        let internalMachRec = row.find('.internalMachRec').length ? 1 : 0;
        let materialType = row.find('.materialType').text();
        let inventoryImpact = row.find('.inventoryImpact').text();
        let ex = row.find('.ex').text().trim();
        let custDrawing = row.find('.custDrawing').text();
        let custDrawingRev = row.find('.custDrawingRev').text();
        let weight = row.find('.weight').text();
        let weightUnit = row.find('.weightUnit').text();
        let material = row.find('.material').text();
        let platingSurface = row.find('.platingSurface').text();
        let platingSurfaceUnit = row.find('.platingSurfaceUnit').text();
        let eccn = row.find('.eccn').text();
        let rdo = row.find('.rdo').text();
        let hts = row.find('.hts').text();
        let comments = row.find('.comments').find('i').attr('data-bs-title');

        $('#document_id').val(id);
        $('#reference').val(reference);
        $('#refRev').val(refRev);
        $('#idAletiq').val(idAletiq);
        $('#prodDraw').val(prodDraw.trim());
        $('#prodDrawRev').val(prodDrawRev);
        $('#alias').val(alias);
        updateCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#refTitleFra').val(refTitleFra);
        updateTitleCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#action').selectpicker('val', action);
        $('#docType').selectpicker('val', docType);
        $('#internalMachRec').prop('checked', internalMachRec);
        $('#materialType').selectpicker('val', materialType);
        $('#inventoryImpact').selectpicker('val', inventoryImpact);
        $('#ex').selectpicker('val', ex);
        $('#custDrawing').val(custDrawing);
        $('#custDrawingRev').val(custDrawingRev);
        $('#weight').val(weight);
        $('#weightUnit').selectpicker('val', weightUnit);
        // Charger les matériaux du document
        if (window.materialTagsInstance) {
            // Récupérer les matériaux du document via AJAX
            loadDocumentMaterials(id);
        }
        $('#platingSurface').val(platingSurface);
        $('#platingSurfaceUnit').selectpicker('val', platingSurfaceUnit);
        $('#eccn').val(eccn);
        $('#rdo').val(rdo);
        $('#hts').val(hts);
        // Ne pas charger automatiquement les anciens commentaires dans le champ de saisie
        // $('#comments').val(comments);

        $('.document_row').removeClass('selected-row');
        row.addClass('selected-row');

        $('#add-document').hide();
        $('#delete-document').show();
        refreshIframe();
        remove_border_red();

        // Charger le nombre de commentaires pour mettre à jour le bouton
        loadCommentsCount(id);

        // Valider la longueur de la référence selon le doc type
        setTimeout(validateReferenceLength, 200);
    }


    $(document).on('click', '.document_row', function(e) {
        fill_fields_document.call(this, e);
    });

    $(document).on('change', '#reference', function() {
        let reference = $(this).val();
        $.ajax({
            url: "{{ path('find_document') }}",
            type: 'POST',
            data: {reference: reference},
            success: function(response) {
                console.log("Réponse complète:", response);
                if (response.error) {
                    console.error("Erreur:", response.error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur: ' + response.error
                    });
                    return;
                }

                response = response.document;
                if (response) {
                    console.log("Document trouvé:", response);
                    // Stocker les données du document trouvé dans la variable globale
                    foundDocumentData = response;
                    Toast.fire({
                        icon: 'success',
                        title: 'Révision précédente trouvée !'
                    });

                    // Remplir tous les champs avec les données du document
                    $('#reference').val(response.reference);
                    $('#refRev').val(response.refRev);
                    $('#idAletiq').val(response.idAletiq);
                    // Ne pas remplir le champ Product Drawing pour que l'utilisateur le remplisse lui-même
                    // $('#prodDraw').val(response.prodDraw);
                    // $('#prodDrawRev').val(response.prodDrawRev);
                    $('#alias').val(response.alias);
                    updateCharCount(); // Mettre à jour le compteur après avoir défini la valeur
                    $('#refTitleFra').val(response.refTitleFra);
                    updateTitleCharCount(); // Mettre à jour le compteur après avoir défini la valeur

                    // Champs select avec selectpicker
                    $('#action').selectpicker('val', response.action);
                    $('#docType').selectpicker('val', response.docType);
                    $('#materialType').selectpicker('val', response.Material_Type);
                    $('#inventoryImpact').selectpicker('val', response.inventory_impact);
                    $('#ex').selectpicker('val', response.ex);
                    $('#weightUnit').selectpicker('val', response.WeightUnit);
                    $('#material').selectpicker('val', response.material);
                    $('#platingSurfaceUnit').selectpicker('val', response.PlatingSurfaceUnit);

                    // Champs checkbox
                    $('#internalMachRec').prop('checked', response.internalMachRec);

                    // Autres champs texte
                    $('#custDrawing').val(response.CustDrawing);
                    $('#custDrawingRev').val(response.CustDrawingRev);
                    $('#weight').val(response.Weight);
                    $('#platingSurface').val(response.PlatingSurface);
                    $('#eccn').val(response.eccn);
                    $('#rdo').val(response.rdo);
                    $('#hts').val(response.hts);

                    // Champs supplémentaires qui n'étaient pas remplis auparavant
                    if ($('#cls').length) $('#cls').val(response.cls);
                    if ($('#moq').length) $('#moq').val(response.moq);
                    if ($('#productCode').length) $('#productCode').val(response.productCode);
                    if ($('#prodAgent').length) $('#prodAgent').val(response.prodAgent);
                    if ($('#mof').length) $('#mof').val(response.mof);
                    if ($('#commodityCode').length) $('#commodityCode').val(response.commodityCode);
                    if ($('#purchasingGroup').length) $('#purchasingGroup').val(response.purchasingGroup);
                    if ($('#matProdType').length) $('#matProdType').val(response.matProdType);
                    if ($('#Unit').length) $('#Unit').val(response.Unit);
                    if ($('#leadtime').length) $('#leadtime').val(response.leadtime);
                    if ($('#prisDans1').length) $('#prisDans1').val(response.prisDans1);
                    if ($('#prisDans2').length) $('#prisDans2').val(response.prisDans2);
                    if ($('#fia').length) $('#fia').val(response.fia);
                    if ($('#metroTime').length) $('#metroTime').val(response.metroTime);
                    if ($('#procType').length) $('#procType').val(response.procType);

                    // Champs de type tableau
                    if ($('#qInspection').length && response.qInspection) {
                        if (Array.isArray(response.qInspection)) {
                            $('#qInspection').val(response.qInspection.join(', '));
                        } else {
                            $('#qInspection').val(response.qInspection);
                        }
                    }
                    if ($('#qDynamization').length) $('#qDynamization').val(response.qDynamization);
                    if ($('#qDocRec').length && response.qDocRec) {
                        if (Array.isArray(response.qDocRec)) {
                            $('#qDocRec').val(response.qDocRec.join(', '));
                        } else {
                            $('#qDocRec').val(response.qDocRec);
                        }
                    }
                    if ($('#qControlRouting').length) $('#qControlRouting').val(response.qControlRouting);
                    if ($('#criticalComplete').length) $('#criticalComplete').val(response.criticalComplete);
                    if ($('#metroControl').length && response.metroControl) {
                        if (Array.isArray(response.metroControl)) {
                            $('#metroControl').val(response.metroControl.join(', '));
                        } else {
                            $('#metroControl').val(response.metroControl);
                        }
                    }

                    // Champs de type booléen
                    if ($('#switchAletiq').length) $('#switchAletiq').prop('checked', response.switchAletiq);
                    if ($('#docImpact').length) $('#docImpact').prop('checked', response.docImpact);

                    // Pour les champs select avec selectpicker qui n'étaient pas remplis auparavant
                    if ($('#matProdType').hasClass('selectpicker')) $('#matProdType').selectpicker('val', response.matProdType);
                    if ($('#procType').hasClass('selectpicker')) $('#procType').selectpicker('val', response.procType);
                    if ($('#Unit').hasClass('selectpicker')) $('#Unit').selectpicker('val', response.Unit);
                    if ($('#productCode').hasClass('selectpicker')) $('#productCode').selectpicker('val', response.productCode);
                    if ($('#commodityCode').hasClass('selectpicker')) $('#commodityCode').selectpicker('val', response.commodityCode);
                    if ($('#purchasingGroup').hasClass('selectpicker')) $('#purchasingGroup').selectpicker('val', response.purchasingGroup);

                    // Gestion des commentaires
                    // La méthode toJson() retourne les commentaires dans commentaires_By_Owner pour les commentaires principaux
                    if (response.commentaires_By_Owner && response.commentaires_By_Owner.length > 0) {
                        $('#comments').val(response.commentaires_By_Owner.join("\n"));
                    }
                    // Fallback sur l'ancienne structure si nécessaire
                    else if (response.commentaires && response.commentaires.length > 0) {
                        if (Array.isArray(response.commentaires)) {
                            // Si c'est un tableau d'objets (ancienne structure)
                            if (typeof response.commentaires[0] === 'object') {
                                let comments = '';
                                for (let i = 0; i < response.commentaires.length; i++) {
                                    if (response.commentaires[i].type === 'principal') {
                                        comments += response.commentaires[i].commentaire + '\n';
                                    }
                                }
                                $('#comments').val(comments);
                            }
                            // Si c'est un tableau de chaînes
                            else {
                                $('#comments').val(response.commentaires.join("\n"));
                            }
                        }
                        // Si c'est une chaîne
                        else {
                            $('#comments').val(response.commentaires);
                        }
                    }

                    // Charger le nombre de commentaires pour mettre à jour le bouton
                    if (response.id) {
                        loadCommentsCount(response.id);
                    }
                }
            },
            error: function(xhr, status, error){
                console.error("Erreur AJAX:", status, error);
                console.log("Réponse d'erreur:", xhr.responseText);
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur: ' + (errorResponse.error || error)
                    });
                } catch (e) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur: ' + error
                    });
                }
            }
        });
    });

    // Variable globale pour stocker l'ID du document actuel
    let currentDocumentId = null;

    // Fonction pour afficher le modal des commentaires
    function showCommentsModal(documentId, documentReference) {
        // Stocker l'ID du document pour l'ajout de commentaires
        currentDocumentId = documentId;

        // Mettre à jour le titre du modal
        document.getElementById('documentReference').textContent = documentReference;

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('modalComments'));
        modal.show();

        // Charger les commentaires
        loadCommentsForModal(documentId);
    }

    // Fonction pour ajouter un nouveau commentaire
    function addNewComment() {
        const commentTextarea = document.getElementById('newCommentText');
        const commentText = commentTextarea.value.trim();

        if (!commentText) {
            Toast.fire({
                icon: 'warning',
                title: 'Veuillez saisir un commentaire'
            });
            return;
        }

        if (!currentDocumentId) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur: Document non identifié'
            });
            return;
        }

        // Désactiver le formulaire pendant l'envoi
        const form = document.querySelector('.modal-footer');
        form.classList.add('comment-form-loading');
        document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Ajout...';

        // Appeler l'API pour ajouter le commentaire
        $.ajax({
            url: "{{ path('app_commentaire_new') }}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                documentId: currentDocumentId,
                content: commentText,
                type: 'principal',
                state: 'package'
            }),
            success: function(response) {
                form.classList.remove('comment-form-loading');
                document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

                // Vider le champ de commentaire
                commentTextarea.value = '';

                Toast.fire({
                    icon: 'success',
                    title: 'Commentaire ajouté'
                });

                // Recharger les commentaires dans le modal après succès
                setTimeout(() => {
                    loadCommentsForModal(currentDocumentId);
                }, 200);
            },
            error: function(xhr) {
                form.classList.remove('comment-form-loading');
                document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de l\'ajout du commentaire'
                });
            }
        });
    }

    // Fonction séparée pour charger les commentaires
    function loadCommentsForModal(documentId) {
        document.getElementById('commentsModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2 text-muted">Chargement des commentaires...</p>
            </div>
        `;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                displayComments(comments);
            },
            error: function(xhr, status, error) {
                document.getElementById('commentsModalBody').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des commentaires: ${error}
                    </div>
                `;
            }
        });
    }

    // Fonction pour afficher les commentaires dans le modal
    function displayComments(comments) {
        const modalBody = document.getElementById('commentsModalBody');

        if (!comments || comments.length === 0) {
            modalBody.innerHTML = `
                <div class="no-comments">
                    <i class="fas fa-comment-slash fa-3x mb-3" style="color: #ccc;"></i>
                    <p>Aucun commentaire pour ce document.</p>
                </div>
            `;
            return;
        }

        let html = `<div class="comments-count">
            <i class="fas fa-comments me-2"></i>
            ${comments.length} commentaire${comments.length > 1 ? 's' : ''}
        </div>`;

        comments.forEach(comment => {
            const createdAt = new Date(comment.created_at);
            const formattedDate = createdAt.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            html += `
                <div class="comment-item">
                    <div class="comment-header">
                        <span class="comment-state">${comment.state ? comment.state.toUpperCase() : 'GÉNÉRAL'}</span>
                        <div class="comment-meta">
                            <i class="fas fa-user me-1"></i>
                            ${comment.user ? comment.user.prenom + ' ' + comment.user.nom : 'Utilisateur inconnu'}
                            <i class="fas fa-clock ms-3 me-1"></i>
                            ${formattedDate}
                        </div>
                    </div>
                    <div class="comment-content">
                        ${comment.commentaire}
                    </div>
                </div>
            `;
        });

        modalBody.innerHTML = html;
    }

    // Affiche/Recharge le tooltip au survol
    function loadCommentsTooltip(iconElem) {
        const documentId = $(iconElem).data('document-id');
        const txt = $(iconElem).attr('txt') || '<em>Aucun commentaire</em>';

        // Mettre à jour le tooltip avec le contenu
        $(iconElem).attr('data-bs-original-title', txt);

        // Réinitialiser le tooltip
        const tooltip = bootstrap.Tooltip.getInstance(iconElem);
        if (tooltip) {
            tooltip.dispose();
        }
        new bootstrap.Tooltip(iconElem, {
            html: true,
            placement: 'top'
        });
    }

    // Fonction pour afficher les commentaires antérieurs du document actuellement chargé dans le formulaire
    function showPreviousCommentsForCurrentDocument() {
        if (!currentFormDocumentId) {
            Toast.fire({
                icon: 'warning',
                title: 'Aucun document sélectionné'
            });
            return;
        }

        // Utiliser la fonction existante du modal avec l'ID du document du formulaire
        const reference = $('#reference').val() || 'Document';
        showCommentsModal(currentFormDocumentId, reference);
    }

    // Fonction pour mettre à jour le bouton des commentaires antérieurs
    function updatePreviousCommentsButton(documentId, commentsCount = 0) {
        currentFormDocumentId = documentId;
        const button = $('#viewPreviousCommentsBtn');
        const countSpan = $('#commentsCount');

        if (documentId && commentsCount > 0) {
            button.show();
            countSpan.text(commentsCount);
        } else if (documentId) {
            // Afficher le bouton même s'il n'y a pas de commentaires pour permettre d'en ajouter
            button.show();
            countSpan.text(0);
        } else {
            button.hide();
            currentFormDocumentId = null;
        }
    }

    // Fonction pour charger le nombre de commentaires d'un document
    function loadCommentsCount(documentId) {
        if (!documentId) return;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                const count = comments ? comments.length : 0;
                updatePreviousCommentsButton(documentId, count);
            },
            error: function(xhr, status, error) {
                // En cas d'erreur, afficher quand même le bouton
                updatePreviousCommentsButton(documentId, 0);
            }
        });
    }

    // Rendre les fonctions globales pour qu'elles soient accessibles depuis le HTML
    window.showCommentsModal = showCommentsModal;
    window.addNewComment = addNewComment;
    window.loadCommentsTooltip = loadCommentsTooltip;
    window.showPreviousCommentsForCurrentDocument = showPreviousCommentsForCurrentDocument;

</script>

<!-- CSS pour les matériaux -->
<link rel="stylesheet" href="{{ asset('css/material-tags.css') }}">

<!-- Scripts pour les matériaux -->
<script src="{{ asset('js/material-datalist.js') }}"></script>
<script src="{{ asset('js/material-tags.js') }}"></script>

<script>
    // Variable globale pour l'instance des matériaux
    window.materialTagsInstance = null;

    // Fonction pour charger les matériaux d'un document
    function loadDocumentMaterials(documentId) {
        $.ajax({
            url: "/index.php/api/document/" + documentId + "/materials",
            type: 'GET',
            success: function(materials) {
                if (window.materialTagsInstance && materials.length > 0) {
                    window.materialTagsInstance.setMaterials(materials);
                }
            },
            error: function(xhr, status, error) {
                // Erreur silencieuse - les matériaux ne seront pas chargés
            }
        });
    }

    // Fonction pour vérifier les matériaux FMTS et mettre à jour les champs obligatoires
    function checkFMTSMaterials() {
        let hasFMTSMaterial = false;
        $('.material-tag').each(function() {
            const materialText = $(this).text().trim();
            // Enlever le bouton de fermeture du texte
            const materialReference = materialText.replace(/×$/, '').trim();
            if (materialReference.includes('FMTS')) {
                hasFMTSMaterial = true;
                return false; // Sortir de la boucle
            }
        });

        // Mettre à jour l'apparence des champs Plating Surface
        const platingSurfaceField = $('#platingSurface');
        const platingSurfaceUnitField = $('#platingSurfaceUnit');

        if (hasFMTSMaterial) {
            // Ajouter un indicateur visuel que ces champs sont obligatoires
            platingSurfaceField.attr('data-fmts-required', 'true');
            platingSurfaceUnitField.attr('data-fmts-required', 'true');

            // Ajouter une classe CSS pour l'indication visuelle
            platingSurfaceField.addClass('fmts-required');
            platingSurfaceUnitField.next().addClass('fmts-required'); // Pour selectpicker
        } else {
            // Supprimer l'indicateur
            platingSurfaceField.removeAttr('data-fmts-required').removeClass('fmts-required');
            platingSurfaceUnitField.removeAttr('data-fmts-required');
            platingSurfaceUnitField.next().removeClass('fmts-required');
        }
    }

    // Initialiser le système de matériaux après le chargement de la page
    $(document).ready(function() {
        // Vérifier que les classes sont disponibles
        if (typeof MaterialTags !== 'undefined' && typeof MaterialDatalist !== 'undefined') {
            // Initialiser le composant de tags de matériaux
            window.materialTagsInstance = new MaterialTags(document.getElementById('materials-container'), {
                inputName: 'materials[]',
                placeholder: 'Rechercher un matériau...',
                maxTags: 5
            });

            // Observer les changements de matériaux pour vérifier FMTS
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        checkFMTSMaterials();
                    }
                });
            });

            // Observer les changements dans le conteneur des matériaux
            const materialsContainer = document.getElementById('materials-container');
            if (materialsContainer) {
                observer.observe(materialsContainer, { childList: true, subtree: true });
            }
        }
    });
</script>
{% endblock %}
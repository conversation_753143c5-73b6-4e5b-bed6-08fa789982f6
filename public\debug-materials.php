<?php
// Script de debug simple pour tester l'API des matériaux
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Matériaux API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Debug API Matériaux</h1>
        
        <div class="row">
            <div class="col-md-12">
                <h3>Test des URLs</h3>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="testUrl('/index.php/api/materials/search?q=FMT')">Test Search API</button>
                    <button class="btn btn-secondary" onclick="testUrl('/index.php/api/materials/all')">Test All Materials</button>
                    <button class="btn btn-info" onclick="testUrl('/index.php/api/document/1/materials')">Test Document Materials</button>
                </div>
                
                <div id="results" class="mt-3"></div>
                
                <h3 class="mt-5">Test du composant MaterialTags</h3>
                <div class="material-tags" id="test-materials-container">
                    <!-- Le composant sera initialisé ici -->
                </div>
                
                <div id="component-debug" class="mt-3 alert alert-info">
                    <h5>Component Debug:</h5>
                    <div id="component-debug-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/material-datalist.js"></script>
    <script src="js/material-tags.js"></script>
    
    <script>
        function testUrl(url) {
            $('#results').append('<div class="alert alert-info">Testing: ' + url + '</div>');
            
            $.ajax({
                url: url,
                type: 'GET',
                success: function(data) {
                    $('#results').append('<div class="alert alert-success"><strong>Success:</strong> ' + url + '<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>');
                },
                error: function(xhr, status, error) {
                    $('#results').append('<div class="alert alert-danger"><strong>Error:</strong> ' + url + '<br>Status: ' + status + '<br>Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }
        
        $(document).ready(function() {
            $('#component-debug-content').append('<p>Document ready</p>');
            
            // Vérifier si les classes sont disponibles
            if (typeof MaterialTags !== 'undefined') {
                $('#component-debug-content').append('<p class="text-success">✓ MaterialTags class found</p>');
            } else {
                $('#component-debug-content').append('<p class="text-danger">✗ MaterialTags class NOT found</p>');
            }
            
            if (typeof MaterialDatalist !== 'undefined') {
                $('#component-debug-content').append('<p class="text-success">✓ MaterialDatalist class found</p>');
            } else {
                $('#component-debug-content').append('<p class="text-danger">✗ MaterialDatalist class NOT found</p>');
            }
            
            try {
                const materialTagsInstance = new MaterialTags(document.getElementById('test-materials-container'), {
                    inputName: 'materials[]',
                    placeholder: 'Rechercher un matériau...',
                    maxTags: 5
                });
                
                $('#component-debug-content').append('<p class="text-success">✓ MaterialTags instance created successfully</p>');

                // Écouter les événements
                document.getElementById('test-materials-container').addEventListener('materialAdded', function(e) {
                    console.log('Matériau ajouté:', e.detail);
                    $('#component-debug-content').append('<p class="text-info">Material added: ' + e.detail.reference + '</p>');
                });

                document.getElementById('test-materials-container').addEventListener('materialRemoved', function(e) {
                    console.log('Matériau supprimé:', e.detail);
                    $('#component-debug-content').append('<p class="text-warning">Material removed: ' + e.detail.id + '</p>');
                });
            } catch (error) {
                $('#component-debug-content').append('<p class="text-danger">✗ Error creating MaterialTags: ' + error.message + '</p>');
                console.error('MaterialTags error:', error);
            }
        });
    </script>
</body>
</html>

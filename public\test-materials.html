<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Matériaux</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Test du système de matériaux</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test API Matériaux</h3>
                <button id="testApiBtn" class="btn btn-primary">Tester API /api/materials/search</button>
                <div id="apiResults" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Composant Tags</h3>
                <div class="material-tags" id="test-materials-container">
                    <!-- Test avec des matériaux existants -->
                </div>
                <div id="debug-info" class="mt-3 alert alert-info">
                    <h5>Debug Info:</h5>
                    <div id="debug-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/material-datalist.js"></script>
    <script src="js/material-tags.js"></script>
    
    <script>
        // Test de l'API
        $('#testApiBtn').click(function() {
            $('#debug-content').append('<p>Testing API...</p>');
            
            $.ajax({
                url: '/index.php/api/materials/search?q=FMT',
                type: 'GET',
                success: function(data) {
                    $('#apiResults').html('<div class="alert alert-success">API fonctionne ! Résultats: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>');
                    $('#debug-content').append('<p class="text-success">API Success: ' + data.length + ' results</p>');
                },
                error: function(xhr, status, error) {
                    $('#apiResults').html('<div class="alert alert-danger">Erreur API: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                    $('#debug-content').append('<p class="text-danger">API Error: ' + error + '</p>');
                }
            });
        });

        // Test du composant
        $(document).ready(function() {
            $('#debug-content').append('<p>Document ready</p>');
            
            // Vérifier si les classes sont disponibles
            if (typeof MaterialTags !== 'undefined') {
                $('#debug-content').append('<p class="text-success">MaterialTags class found</p>');
            } else {
                $('#debug-content').append('<p class="text-danger">MaterialTags class NOT found</p>');
            }
            
            if (typeof MaterialDatalist !== 'undefined') {
                $('#debug-content').append('<p class="text-success">MaterialDatalist class found</p>');
            } else {
                $('#debug-content').append('<p class="text-danger">MaterialDatalist class NOT found</p>');
            }
            
            try {
                const materialTagsInstance = new MaterialTags(document.getElementById('test-materials-container'), {
                    inputName: 'materials[]',
                    placeholder: 'Rechercher un matériau...',
                    maxTags: 5
                });
                
                $('#debug-content').append('<p class="text-success">MaterialTags instance created successfully</p>');

                // Écouter les événements
                document.getElementById('test-materials-container').addEventListener('materialAdded', function(e) {
                    console.log('Matériau ajouté:', e.detail);
                    $('#debug-content').append('<p class="text-info">Material added: ' + e.detail.reference + '</p>');
                });

                document.getElementById('test-materials-container').addEventListener('materialRemoved', function(e) {
                    console.log('Matériau supprimé:', e.detail);
                    $('#debug-content').append('<p class="text-warning">Material removed: ' + e.detail.id + '</p>');
                });
            } catch (error) {
                $('#debug-content').append('<p class="text-danger">Error creating MaterialTags: ' + error.message + '</p>');
            }
        });
    </script>
</body>
</html>

<?php

namespace App\Repository;

use App\Entity\Material;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Material>
 */
class MaterialRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Material::class);
    }

    /**
     * Trouve un matériau par sa référence
     */
    public function findByReference(string $reference): ?Material
    {
        return $this->findOneBy(['reference' => $reference]);
    }

    /**
     * Trouve tous les matériaux actifs
     */
    public function findActive(): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.status = :status')
            ->setParameter('status', 'ACTIVE')
            ->orderBy('m.reference', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche de matériaux par référence ou description
     */
    public function search(string $term): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.reference LIKE :term OR m.description LIKE :term')
            ->setParameter('term', '%' . $term . '%')
            ->orderBy('m.reference', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve un matériau par son ancien ID (pour la migration)
     */
    public function findByLegacyId(int $legacyId): ?Material
    {
        return $this->findOneBy(['legacyId' => $legacyId]);
    }
}

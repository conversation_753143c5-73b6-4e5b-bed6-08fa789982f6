<?php

namespace App\Controller\Api;

use App\Repository\DocumentRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/document', name: 'api_document_')]
class DocumentController extends AbstractController
{
    public function __construct(
        private DocumentRepository $documentRepository
    ) {}

    #[Route('/{id}/materials', name: 'materials', methods: ['GET'])]
    public function getMaterials(int $id): JsonResponse
    {
        $document = $this->documentRepository->find($id);
        
        if (!$document) {
            return new JsonResponse(['error' => 'Document not found'], 404);
        }

        $results = [];
        foreach ($document->getMaterials() as $material) {
            $results[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ($material->getDescription() ? ' - ' . $material->getDescription() : '')
            ];
        }

        return new JsonResponse($results);
    }
}

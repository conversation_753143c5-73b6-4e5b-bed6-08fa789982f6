<?php

namespace App\Controller\Api;

use App\Repository\MaterialRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/materials', name: 'api_materials_')]
class MaterialController extends AbstractController
{
    public function __construct(
        private MaterialRepository $materialRepository
    ) {}

    #[Route('/search', name: 'search', methods: ['GET'])]
    public function search(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        $limit = min((int) $request->query->get('limit', 50), 100); // Max 100 résultats

        if (strlen($query) < 2) {
            return new JsonResponse([]);
        }

        $materials = $this->materialRepository->searchByReference($query, $limit);

        $results = [];
        foreach ($materials as $material) {
            $results[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ($material->getDescription() ? ' - ' . $material->getDescription() : '')
            ];
        }

        return new JsonResponse($results);
    }

    #[Route('/all', name: 'all', methods: ['GET'])]
    public function all(Request $request): JsonResponse
    {
        $limit = min((int) $request->query->get('limit', 1000), 2000); // Max 2000 résultats

        $materials = $this->materialRepository->findBy([], ['reference' => 'ASC'], $limit);

        $results = [];
        foreach ($materials as $material) {
            $results[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ($material->getDescription() ? ' - ' . $material->getDescription() : '')
            ];
        }

        return new JsonResponse($results);
    }
}

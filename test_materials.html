<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Matériaux</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Test du système de matériaux</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test API Matériaux</h3>
                <button id="testApiBtn" class="btn btn-primary">Tester API /api/materials/search</button>
                <div id="apiResults" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Composant Tags</h3>
                <div class="material-tags" id="test-materials-container">
                    <!-- Test avec des matériaux existants -->
                    <input type="hidden" name="materials[]" value="1" data-material-reference="FMTP900" data-material-id="1">
                    <input type="hidden" name="materials[]" value="2" data-material-reference="FMTP901" data-material-id="2">
                </div>
            </div>
        </div>
    </div>

    <script src="public/js/material-datalist.js"></script>
    <script src="public/js/material-tags.js"></script>
    
    <script>
        // Test de l'API
        $('#testApiBtn').click(function() {
            $.ajax({
                url: 'http://127.0.0.1:8000/api/materials/search?q=FMT',
                type: 'GET',
                success: function(data) {
                    $('#apiResults').html('<div class="alert alert-success">API fonctionne ! Résultats: ' + JSON.stringify(data, null, 2) + '</div>');
                },
                error: function(xhr, status, error) {
                    $('#apiResults').html('<div class="alert alert-danger">Erreur API: ' + error + '</div>');
                }
            });
        });

        // Test du composant
        $(document).ready(function() {
            const materialTagsInstance = new MaterialTags(document.getElementById('test-materials-container'), {
                inputName: 'materials[]',
                placeholder: 'Rechercher un matériau...',
                maxTags: 5
            });

            // Écouter les événements
            document.getElementById('test-materials-container').addEventListener('materialAdded', function(e) {
                console.log('Matériau ajouté:', e.detail);
            });

            document.getElementById('test-materials-container').addEventListener('materialRemoved', function(e) {
                console.log('Matériau supprimé:', e.detail);
            });
        });
    </script>
</body>
</html>

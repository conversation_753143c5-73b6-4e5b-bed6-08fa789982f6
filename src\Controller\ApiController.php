<?php

namespace App\Controller;

use App\Entity\Document;
use App\Entity\Material;
use App\Repository\MaterialRepository;
use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api')]
class ApiController extends AbstractController
{
    public function __construct(
        private MaterialRepository $materialRepository,
        private DocumentRepository $documentRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/materials/search', name: 'api_materials_search', methods: ['GET'])]
    public function searchMaterials(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        $limit = (int) $request->query->get('limit', 50);

        if (strlen($query) < 2) {
            return $this->json([]);
        }

        $materials = $this->materialRepository->searchByReference($query, $limit);

        $result = [];
        foreach ($materials as $material) {
            $result[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ' - ' . $material->getDescription()
            ];
        }

        return $this->json($result);
    }

    #[Route('/materials/all', name: 'api_materials_all', methods: ['GET'])]
    public function getAllMaterials(): JsonResponse
    {
        $materials = $this->materialRepository->findAll();

        $result = [];
        foreach ($materials as $material) {
            $result[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ' - ' . $material->getDescription()
            ];
        }

        return $this->json($result);
    }

    #[Route('/document/{id}/materials', name: 'api_document_materials', methods: ['GET'])]
    public function getDocumentMaterials(int $id): JsonResponse
    {
        $document = $this->documentRepository->find($id);

        if (!$document) {
            return $this->json(['error' => 'Document not found'], 404);
        }

        $result = [];
        foreach ($document->getMaterials() as $material) {
            $result[] = [
                'id' => $material->getId(),
                'reference' => $material->getReference(),
                'description' => $material->getDescription(),
                'label' => $material->getReference() . ' - ' . $material->getDescription()
            ];
        }

        return $this->json($result);
    }

    #[Route('/document/{id}/materials', name: 'api_document_materials_update', methods: ['POST'])]
    public function updateDocumentMaterials(int $id, Request $request): JsonResponse
    {
        $document = $this->documentRepository->find($id);

        if (!$document) {
            return $this->json(['error' => 'Document not found'], 404);
        }

        $data = json_decode($request->getContent(), true);
        $materialIds = $data['materials'] ?? [];

        // Vider les matériaux existants
        $document->getMaterials()->clear();

        // Ajouter les nouveaux matériaux
        foreach ($materialIds as $materialId) {
            $material = $this->materialRepository->find($materialId);
            if ($material) {
                $document->addMaterial($material);
            }
        }

        $this->entityManager->flush();

        return $this->json(['success' => true]);
    }
}

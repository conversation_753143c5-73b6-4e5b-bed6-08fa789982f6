{% extends 'base.html.twig' %}

{% block title %}Package {{ package.id }}{% endblock %}

{% block body %}
<style>
td{
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

th{
    text-align: center;
    vertical-align: middle;
    background-color: #004080!important;
    color: white!important;
    border: none!important;
    font-size: 10px!important;
}

.badge-hover:hover{
    background-color: #004080!important;
    transition: background-color 0.3s ease;
}

.badge-hover{
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.fw-500{
    font-weight: 500;
}

label{
    white-space: nowrap;
    user-select: none;
}

#document-form label{
    min-width: 13rem!important;
    width: 13rem!important;
    max-width: 13rem!important;
}

.form-label-sm{
    font-size: .875rem;
}

/* Styles pour le modal des commentaires */
.comment-item {
    background-color: #f8f9fa;
    border-left: 4px solid #009BFF;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-state {
    background-color: #009BFF;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-content {
    color: #333;
    line-height: 1.5;
    margin-top: 8px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.comments-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

/* Styles pour le formulaire d'ajout de commentaire */
.modal-footer.custom-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#newCommentText {
    resize: vertical;
    min-height: 38px;
}

#addCommentBtn {
    background-color: #009BFF;
    border-color: #009BFF;
    height: 38px;
}

#addCommentBtn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.comment-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Modale personnalisée */
.modal-dialog.modal-lg {
    max-width: 900px;
}

.modal-content.custom-modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.modal-header.custom-modal-header {
    background-color: #004080;
    color: #fff;
    border-bottom: none;
}

.modal-header.custom-modal-header .btn-close {
    filter: invert(100%);
}

/* Retirer la transparence des tooltips pour une meilleure lisibilité */
.tooltip .tooltip-inner {
    background-color: rgba(0, 0, 0, 0.95) !important;
    opacity: 1 !important;
    max-width: 400px;
    overflow-y: auto;
    white-space: normal;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before,
.tooltip.bs-tooltip-bottom .tooltip-arrow::before,
.tooltip.bs-tooltip-start .tooltip-arrow::before,
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-color: rgba(0, 0, 0, 0.95) transparent !important;
}

.first{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    width: 90%;
}

.second{
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    width: 15%;
}

.selected-row td{
    background-color:rgba(62, 156, 250, 0.45)!important;
    border: 1px rgba(62, 156, 250, 0.45)!important;
}

textarea{
    transition: all 0.3s ease;
}

</style>

<div class="mb-0 rounded-0">
    <div class="card border-0">
        <div style="background-color: #004080;" class="card-header border-0 text-white d-flex justify-content-between align-items-center rounded-0">
            <h3 class="card-title p-0 mb-0">Validation Package - {{ package.id }}</h3>
        </div>
        <div class="card-body row mx-0 p-0">
            {# DOCUMENT FORM COL #}
            <input type="hidden" id="document_id" name="document_id" value="">
            <div class="border-end col-md-7" id="document-form">
                <div class="row mx-0">
                    <div class="col-md-6">
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="reference">Reference Article (SAP)</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="reference" name="reference" placeholder="Reference" required>
                                <input type="text" class="form-control form-control-sm second" id="refRev" name="refRev" placeholder="Rev" required>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="prodDraw">Product Drawing</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="prodDraw" name="prodDraw" placeholder="ProdDraw" required>
                                <input type="text" class="form-control form-control-sm second" id="prodDrawRev" name="prodDrawRev" placeholder="Rev" required>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="alias">Alias</label>
                            <div class="w-100 position-relative">
                                <input type="text" class="form-control form-control-sm" id="alias" name="alias" placeholder="Alias" maxlength="40">
                                <small id="charCount" class="position-absolute end-0 top-0 text-muted mt-2 me-2" style="font-size: 0.7rem;">0/40</small>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="refTitleFra">Title</label>
                            <div class="w-100 position-relative">
                                <input type="text" class="form-control form-control-sm" id="refTitleFra" name="refTitleFra" placeholder="Title" maxlength="40" required>
                                <small id="titleCharCount" class="position-absolute end-0 top-0 text-muted mt-2 me-2" style="font-size: 0.7rem;">0/40</small>
                            </div>
                        </div>
                        <!-- Messages d'alerte pour les limites de caractères -->
                        <div id="charLimitAlert" class="alert alert-warning alert-dismissible fade mt-2" role="alert" style="display: none; font-size: 0.8rem; padding: 0.5rem;">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <span id="alertMessage"></span>
                            <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert" aria-label="Close" style="font-size: 0.7rem;"></button>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="action">Action</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="action" name="action" required title="Action" >
                                <option value="Modification">Modification</option>
                                <option value="Suppression">Suppression</option>
                                <option value="Création">Création</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="docType">Type</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="docType" name="docType" required title="Type" >
                                <option value ="PUR" title="PUR">PUR - Purchased </option>
                                <option value ="MOLD" title="MOLD">MOLD - Molded Part (Internal or External)</option>
                                <option value ="MACH" title="MACH">MACH - Machined Part (Internal or External)</option>
                                <option value ="DOC" title="DOC">DOC - PC, ID, E-, AD</option>
                                <option value ="ASSY" title="ASSY">ASSY - Marking, BoM, GA, Assembly, FT for ZPF BoM driven by FT</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="rdo">RDO</label>
                            <input type="text" class="form-control form-control-sm" id="rdo" name="rdo" placeholder="RDO" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="eccn">ECCN</label>
                            <input type="text" class="form-control form-control-sm" id="eccn" name="eccn" placeholder="ECCN" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="materialType">Material Type</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="materialType" name="materialType" required title="Material Type" >
                                <option value ="SEMI-FINISHED PRODUCT" >SEMI-FINISHED PRODUCT</option>
                                <option value ="RAW MATERIAL" >RAW MATERIAL</option>
                                <option value ="PACKAGING" >PACKAGING</option>
                                <option value ="NON VALUATED MATERIAL" >NON VALUATED MATERIAL</option>
                                <option value ="LITERATURE" >LITERATURE</option>
                                <option value ="FINISHED PRODUCT" >FINISHED PRODUCT</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="inventoryImpact">Inventory Impact</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="inventoryImpact" name="inventoryImpact" required title="Inventory Impact" >
                                <option value="TO BE UPDATED" test="Ongoing orders in SCM, orders at suppliers or parts in stock at SCM must be reworkedn ">TO BE UPDATED</option>
                                <option value="TO BE SCRAPPED" test="Every parts in stock, being used in assembly, or being manufactured/ordered must be scrapped ">TO BE SCRAPPED</option>
                                <option value="NO IMPACT" test="No impact on the current stock or any ongoing orders or manufacturing">NO IMPACT</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="ex">Ex</label>
                            <select class="selectpicker" data-style="border btn btn-sm bg-white" data-width="100%" id="ex" name="ex" required title="Ex">
                                <option selected value=""></option>
                                <option value="NO">NO</option>
                                <option value="IECEX">IECEX</option>
                                <option value="CSA">CSA</option>
                                <option value="ATEX">ATEX</option>
                            </select>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="custDrawing">Cust. Drawing</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="custDrawing" name="custDrawing" placeholder="Cust. Drawing" >
                                <input type="text" class="form-control form-control-sm second" id="custDrawingRev" name="custDrawingRev" placeholder="Rev" >
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="weight">Weight in air</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="number" class="form-control form-control-sm first" id="weight" name="weight" placeholder="Weight" required>
                                <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="weightUnit" name="weightUnit" required title="Unit" >
                                    <option value=""></option>
                                    <option value="g">g</option>
                                    <option value="kg">kg</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-2">
                            <label class="fw-500 form-label-sm" for="materials">Matériaux</label>
                            <div class="material-tags" id="materials-container">
                                <!-- Les matériaux existants seront chargés ici dynamiquement -->
                            </div>
                            <small class="form-text text-muted">Tapez pour rechercher et sélectionner des matériaux</small>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="platingSurface">Plating Surface</label>
                            <div class="d-flex align-items-center w-100">
                                <input type="text" class="form-control form-control-sm first" id="platingSurface" name="platingSurface" placeholder="Plating Surface" >
                                <select class="selectpicker" data-style="border btn btn-sm bg-white second" data-width="15%" id="platingSurfaceUnit" name="platingSurfaceUnit" title="Unit" >
                                    <option value=""></option>
                                    <option value="mm²">mm²</option>
                                    <option value="cm²">cm²</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="hts">HTS</label>
                            <input type="text" class="form-control form-control-sm" id="hts" name="hts" placeholder="HTS" >
                        </div>
                        <div class="mt-2 d-flex align-items-center">
                            <label class="fw-500 form-label-sm" for="internalMachRec">Recommanded Inhouse Manuf.</label>
                            <div class="w-100">
                                <input type="checkbox" class="form-check-input mt-2 me-2" id="internalMachRec" name="internalMachRec" placeholder="internalMachRec" style="transform: scale(1.5);">
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2 w-100">
                        <div class="mt-2 mb-2 w-100">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <label class="fw-500 form-label-sm" for="comments">Commentaire</label>
                                <button type="button"
                                        class="btn btn-outline-primary btn-sm"
                                        id="viewPreviousCommentsBtn"
                                        onclick="showPreviousCommentsForCurrentDocument()"
                                        style="font-size: 0.75rem; padding: 2px 8px; display: none;">
                                    <i class="fas fa-eye me-1"></i>
                                    Voir antérieurs (<span id="commentsCount">0</span>)
                                </button>
                            </div>
                            <textarea class="form-control form-control-sm" id="comments" name="comments" placeholder="Commentaire" rows="3"></textarea>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-md-5 p-0 row-cols-1 mx-0" style="background-color: #F1F3F5;">
                {# PREVIEW row #}
                <iframe id="aletiq_preview" class="col" style="width:100%;height:100%;" src=""></iframe>
            </div>
            <div class="mx-0 p-0 pb-3 row border-top" style="background-color: #EFF1F2;">
                {# COL PACKAGE #}
                <div class="col-11" >
                    <h6 class="fw-500 text-muted m-0">Package info</h6>
                    <div class="d-flex align-items-center justify-content-around">
                        <div class="form-group">
                            <label class="fw-500" for="owner">Propriétaire</label>
                            <p class="fw-bold m-0" ><i class="fas fa-user"></i> {{ package.owner }}</p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Validation</label>
                            <p class="fw-bold m-0" ><i class="fas fa-user"></i> {{ package.verif }}</p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Réservation</label>
                            <p class="fw-bold m-0" >
                                {% if package.ReservationDate is not null %}
                                    {{ package.ReservationDate|date('Y-m-d') }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="form-group">
                            <label class="fw-500" for="owner">Release</label>
                            <p class="fw-bold m-0" >
                                {% if package.DateBE0 is not null %}
                                    {{ package.DateBE0|date('Y-m-d') }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="activity">Activité</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="activity" name="activity" required title="Activité" >
                                <option value="OSI">OSI</option>
                                <option value="MOB_INDUS">MOB_INDUS</option>
                                <option value="MOB_AERO">MOB_AERO</option>
                                <option value="METHOD">METHOD</option>
                                <option value="ENERGY_SIGNAL">ENERGY_SIGNAL</option>
                                <option value="ENERGY_RENEW">ENERGY_RENEW</option>
                                <option value="ENERGY_POWER">ENERGY_POWER</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="ex-package">Ex</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="ex-package" name="ex-package" required title="Ex" >
                                <option value="NO">NO</option>
                                <option value="IECEX">IECEX</option>
                                <option value="CSA">CSA</option>
                                <option value="ATEX">ATEX</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="dmo">DMO</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="dmo" name="dmo[]" required multiple title="DMO" data-live-search="true" data-size="10" data-selected-text-format="count > 2">
                                {% for otp, dmoList in dmobyProjects %}
                                    <optgroup label="{{ otp }}">
                                        {% for dmo in dmoList %}
                                            <option value="{{ dmo.id }}" data-project="{{ otp }}">
                                                {{ dmo.id_dmo }}
                                            </option>
                                        {% endfor %}
                                    </optgroup>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="fw-500" for="project">Projet</label>
                            <select class="selectpicker" data-style="border btn bg-white btn-sm" data-width="100%" id="project" name="project" required data-live-search="true" data-size="10">
                                {% for project in projects %}
                                    {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                                        <option value="{{ project.id }}" selected>{{ project.otp }}</option>
                                    {% else %}
                                        <option value="{{ project.id }}">{{ project.otp }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <label class="fw-500" for="description">Description</label>
                        <textarea class="form-control form-control-sm" id="description" name="description" rows="2" required style="resize: none;">{{ package.description }}</textarea>
                    </div>
                </div>
                <div class="col-1 d-flex flex-column justify-content-center gap-2">
                    <button class="btn btn-sm btn-outline-secondary" id="update">Update</button>
                    <button class="btn btn-sm btn-success" id="valid">Validation</button>
                    <button class="btn btn-sm btn-danger" id="refuser">Renvoyer</button>
                </div>
            </div>
        </div>
            <div class="col-md-12 p-0" id="div-documents">
                <table class="table table-hover table-sm table-bordered mb-0" id="table-documents">
                    <thead>
                        <tr id="entetes">
                            <th>Action</th>
                            <th colspan="2">Reference</th>
                            <th>ID_ALETIQ</th>
                            <th colspan="2">Product Drawing</th>
                            <th>Title</th>
                            <th>Alias</th>
                            <th>Cust. Drawing</th>
                            <th colspan="2">Doc Type</th>
                            <th>Mat Type</th>
                            <th>Inventory Imp.</th>
                            <th>Ex</th>
                            <th colspan="2">Weight</th>
                            <th colspan="2">Plat. Surface</th>
                            <th>Material</th>
                            <th>ECCN</th>
                            <th>RDO</th>
                            <th>HTS</th>
                            <th>Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in package.documents %}
                        <tr class="document_row" id="document_{{ document.id }}">
                            <td class="action" >{{ document.action }}</td>
                            <td class="reference" >{{ document.reference }}</td>
                            <td class="refRev" >{{ document.refRev }}</td>
                            <td class="prodDraw" >
                                <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                    target="_blank"
                                    class="badge bg-primary preview-tooltip">
                                    {{ document.prodDraw }}
                                </a>
                            </td>
                            <td class="prodDrawRev" >{{ document.prodDrawRev }}</td>
                            <td class="refTitleFra" >{{ document.refTitleFra }}</td>
                            <td class="alias" >{{ document.alias }}</td>
                            <td class="custDrawing" >{{ document.custDrawing }}</td>
                            <td class="docType" >{{ document.docType }}</td>
                            <td class="internalMachRec" >{% if document.internalMachRec == 1 %}<img src="{{ asset('scm.png') }}" alt="scm" style="height: 15px;">{% endif %}</td>
                            <td class="materialType" >{{ document.materialType }}</td>
                            <td class="inventoryImpact" >{{ document.inventoryImpact }}</td>
                            <td class="ex">
                                {% if document.ex != 'NO' %}
                                    <span style="color: red;font-weight: 500">{{ document.ex }}</span>
                                {% else %}
                                    {{ document.ex }}
                                {% endif %}
                            </td>
                            <td class="weight" >{{ document.weight }}</td>
                            <td class="weightUnit" >{{ document.weightUnit }}</td>
                            <td class="platingSurface" >{{ document.platingSurface }}</td>
                            <td class="platingSurfaceUnit" >{{ document.platingSurfaceUnit }}</td>
                            <td class="material" >{{ document.material }}</td>
                            <td class="eccn" >{{ document.eccn }}</td>
                            <td class="rdo" >{{ document.rdo }}</td>
                            <td class="hts" >{{ document.hts }}</td>
                            <td class="comments" >
                                {% if document.commentaires|length > 0 %}
                                    <i class="fas fa-file-alt"
                                    style="color: #009BFF; cursor: pointer;"
                                    data-bs-toggle="tooltip"
                                    data-document-id="{{ document.id }}"
                                    onmouseenter="loadCommentsTooltip(this)"
                                    onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                    title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                    txt="{% for comment in document.commentaires %}
                                    <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                    par <em>{{ comment.user|e }}</em><br>
                                    {% endfor %}"
                                    ></i>
                                {% else %}
                                    <i class="fas fa-file-alt"
                                    style="color: #ccc; cursor: pointer;"
                                    title="Aucun commentaire"
                                    data-bs-toggle="tooltip"
                                    data-document-id="{{ document.id }}"
                                    onmouseenter="loadCommentsTooltip(this)"
                                    onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                    title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                    txt="{% for comment in document.commentaires %}
                                    <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                    par <em>{{ comment.user|e }}</em><br>
                                    {% endfor %}"
                                    ></i>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal des commentaires -->
<div class="modal fade" id="modalComments" tabindex="-1" aria-labelledby="modalCommentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalCommentsLabel">
                    <i class="fas fa-comments me-2"></i>
                    Commentaires - <span id="documentReference"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="commentsModalBody">
                <!-- Les commentaires seront chargés ici -->
            </div>
            <div class="modal-footer custom-modal-footer">
                <div class="w-100">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <span class="badge bg-primary" style="background-color: #009BFF !important; font-size: 0.9rem; padding: 8px 12px;">
                                Package
                            </span>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control form-control-sm"
                                      id="newCommentText"
                                      placeholder="Ajouter un commentaire pour ce document..."
                                      rows="2"></textarea>
                        </div>
                        <div class="col-md-2">
                            <button type="button"
                                    class="btn btn-primary btn-sm w-100"
                                    id="addCommentBtn"
                                    onclick="addNewComment()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Fonction pour afficher le message d'alerte
    function showCharLimitAlert(fieldName) {
        const alertDiv = $('#charLimitAlert');
        const alertMessage = $('#alertMessage');

        alertMessage.text(`Limite maximale de 40 caractères atteinte pour le champ ${fieldName}`);
        alertDiv.removeClass('show').addClass('show').show();

        // Masquer automatiquement après 3 secondes
        setTimeout(function() {
            alertDiv.removeClass('show');
            setTimeout(function() {
                alertDiv.hide();
            }, 150);
        }, 3000);
    }

    // Fonction pour mettre à jour le compteur de caractères pour Alias
    function updateCharCount() {
        const aliasInput = document.getElementById('alias');
        const charCountElement = document.getElementById('charCount');
        const currentLength = aliasInput.value.length;

        // Limiter à 40 caractères et afficher alerte si nécessaire
        if (currentLength > 40) {
            aliasInput.value = aliasInput.value.slice(0, 40);
            showCharLimitAlert('Alias');
        } else if (currentLength === 40) {
            // Afficher le message quand on atteint exactement 40 caractères
            showCharLimitAlert('Alias');
        }

        charCountElement.textContent = Math.min(currentLength, 40) + '/40';
    }

    // Fonction pour mettre à jour le compteur de caractères pour Title
    function updateTitleCharCount() {
        const titleInput = document.getElementById('refTitleFra');
        const charCountElement = document.getElementById('titleCharCount');
        const currentLength = titleInput.value.length;

        // Limiter à 40 caractères et afficher alerte si nécessaire
        if (currentLength > 40) {
            titleInput.value = titleInput.value.slice(0, 40);
            showCharLimitAlert('Title');
        } else if (currentLength === 40) {
            // Afficher le message quand on atteint exactement 40 caractères
            showCharLimitAlert('Title');
        }

        charCountElement.textContent = Math.min(currentLength, 40) + '/40';
    }

    // Attacher les événements aux champs
    $(document).ready(function() {
        const aliasInput = $('#alias');
        const titleInput = $('#refTitleFra');

        // Événements de saisie pour Alias
        aliasInput.on('input keyup paste', updateCharCount);

        // Événements de saisie pour Title
        titleInput.on('input keyup paste', updateTitleCharCount);

        // Observer les changements de valeur (pour les modifications programmatiques)
        const aliasObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    updateCharCount();
                }
            });
        });

        const titleObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    updateTitleCharCount();
                }
            });
        });

        // Observer les changements d'attributs
        if (aliasInput[0]) {
            aliasObserver.observe(aliasInput[0], {
                attributes: true,
                attributeFilter: ['value']
            });
        }

        if (titleInput[0]) {
            titleObserver.observe(titleInput[0], {
                attributes: true,
                attributeFilter: ['value']
            });
        }

        // Utiliser un setInterval pour vérifier périodiquement les changements
        setInterval(function() {
            // Vérification pour Alias
            const currentAliasValue = aliasInput.val();
            const displayedAliasCount = $('#charCount').text();
            const expectedAliasCount = currentAliasValue.length + '/40';

            if (displayedAliasCount !== expectedAliasCount) {
                updateCharCount();
            }

            // Vérification pour Title
            const currentTitleValue = titleInput.val();
            const displayedTitleCount = $('#titleCharCount').text();
            const expectedTitleCount = currentTitleValue.length + '/40';

            if (displayedTitleCount !== expectedTitleCount) {
                updateTitleCharCount();
            }
        }, 100);

        // Mise à jour initiale
        updateCharCount();
        updateTitleCharCount();
    });

    $('#dmo').selectpicker('val', [{% for dmo in package.dmos %}'{{ dmo.id }}',{% endfor %}]);
    $('#activity').selectpicker('val', '{{ package.activity }}');
    $('#ex-package').selectpicker('val', '{{ package.ex }}');
    $('#project').selectpicker('val', '{{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}');

    function handleDMOSelection($select) {
        const selectedOptions = $select.find('option:selected');

        if (selectedOptions.length > 0) {
            // Désactive le select project et ajoute l’option du premier DMO sélectionné
            $('#project').empty();
            $('#project').prop('disabled', true);
            $('#project').append('<option value="' + selectedOptions.first().data('project') + '">' + selectedOptions.first().data('project') + '</option>');
            $('#project').selectpicker('destroy');
            $('#project').selectpicker();

            // Désactive les options DMO qui ne correspondent pas au projet du premier DMO sélectionné
            const selectedProject = selectedOptions.first().data('project');
            $select.find('option').each(function() {
                const isMatchingProject = $(this).data('project') === selectedProject;
                $(this).prop('disabled', !isMatchingProject);
            });
        } else {
            // Réactive le select project et recharge toutes les options via Twig
            $('#project').prop('disabled', false);
            $('#project').empty();
            $('#project').selectpicker('destroy');
            {% for project in projects %}
                {% if package.getProjectRelation is not null and project.otp == package.getProjectRelation.getOTP %}
                    $('#project').append('<option value="{{ project.id }}" selected>{{ project.otp }}</option>');
                {% else %}
                    $('#project').append('<option value="{{ project.id }}">{{ project.otp }}</option>');
                {% endif %}
            {% endfor %}
            $('#project').selectpicker();

            // Réactive toutes les options du select DMO
            $select.find('option').each(function() {
                $(this).prop('disabled', false);
            });
        }
        // Réinitialise le select DMO pour refléter les changements
        $select.selectpicker('destroy');
        $select.selectpicker();
        $select.selectpicker('toggle');
    }

    $(document).on('change', '#dmo', function() {
        handleDMOSelection($(this));
    });

    handleDMOSelection($('#dmo'));
    $('#dmo').selectpicker('toggle');
    function updatePackage(){
        // D'abord sauvegarder le commentaire s'il y en a un
        saveCommentIfExists().then(() => {
            let id = {{ package.id }};
            let owner = $('#owner').text().trim().split(' ')[1];
            let activity = $('#activity').val();
            let ex = $('#ex-package').val();
            let dmo = $('#dmo').val();
            let project = $('#project').val();
            let description = $('#description').val();

            $.ajax({
                url: "{{ path('edit_package', {'id': package.id}) }}",
                type: 'POST',
                data: {
                    owner: owner,
                    activity: activity,
                    ex: ex,
                    dmo: dmo,
                    project: project,
                    description: description
                },
                success: function(response){
                    Toast.fire({
                        icon: 'success',
                        title: 'Package mis à jour !'
                    });
                },
                error: function(response){
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors de la mise à jour !'
                    });
                }
            });
        }).catch((error) => {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors de la sauvegarde du commentaire !'
            });
        });
    }

    $('#update').click(function(){
        updatePackage();
    });

    function validPackage(){
        // D'abord sauvegarder le commentaire s'il y en a un
        saveCommentIfExists().then(() => {
            let id = {{ package.id }};
            $.ajax({
                url: "{{ path('validation_package', {'id': package.id}) }}",
                type: 'POST',
                data: {
                    valid: $('#valid').val()
                },
                success: function(response){
                    Toast.fire({
                        icon: 'success',
                        title: 'Validation réussie !'
                    });
                    window.location.href = "{{ path('app_package') }}#onlget=BE";
                },
                error: function(response){
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors du lancement de la Validation !'
                    });
                }
            });
        }).catch((error) => {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors de la sauvegarde du commentaire !'
            });
        });
    }

    // Fonction pour mettre à jour le package sans sauvegarder les commentaires
    function updatePackageOnly(){
        let id = {{ package.id }};
        let owner = $('#owner').text().trim().split(' ')[1];
        let activity = $('#activity').val();
        let ex = $('#ex-package').val();
        let dmo = $('#dmo').val();
        let project = $('#project').val();
        let description = $('#description').val();

        $.ajax({
            url: "{{ path('edit_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                owner: owner,
                activity: activity,
                ex: ex,
                dmo: dmo,
                project: project,
                description: description
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Package mis à jour !'
                });
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour !'
                });
            }
        });
    }

    // Fonction pour faire la validation sans sauvegarder les commentaires
    function validPackageOnly(){
        let id = {{ package.id }};
        $.ajax({
            url: "{{ path('validation_package', {'id': package.id}) }}",
            type: 'POST',
            data: {
                valid: $('#valid').val()
            },
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Validation réussie !'
                });
                window.location.href = "{{ path('app_package') }}#onlget=BE";
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du lancement de la Validation !'
                });
            }
        });
    }

    function are_u_sure(){
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, Valider ce package !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                // Sauvegarder le commentaire une seule fois, puis faire les deux actions
                saveCommentIfExists().then(() => {
                    // Mettre à jour le package (sans sauvegarder le commentaire à nouveau)
                    updatePackageOnly();
                    // Puis faire la validation (sans sauvegarder le commentaire à nouveau)
                    validPackageOnly();
                }).catch((error) => {
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors de la sauvegarde du commentaire !'
                    });
                });
            }
        });
    }

    $('#valid').click(function(){
        if($('#table-documents tbody tr').length > 0){
            are_u_sure();
        }else{
            Toast.fire({
                icon: 'error',
                title: 'Aucun document à valider !'
            });
        }
    });

    let prodDraw = "";
    let prodDrawRev = "";

    function refreshIframe() {
        if ((prodDraw!==$('#prodDraw').val() || prodDrawRev!==$('#prodDrawRev').val()) && $('#prodDraw').val() && $('#prodDrawRev').val()) {
            prodDraw = $('#prodDraw').val();
            prodDrawRev = $('#prodDrawRev').val();
            $('#aletiq_preview').attr('src', "https://app.aletiq.com/parts/preview/id/"+prodDraw+"/revision/"+prodDrawRev);
        }
        else {
            prodDraw = "";
            prodDrawRev = "";
            $('#aletiq_preview').attr('src', "");
        }
    }

    $('#prodDraw').change(refreshIframe);
    $('#prodDrawRev').change(refreshIframe);

    $(function () {
        $('[data-bs-toggle="tooltip"]').tooltip()
    })

    function validate_document_data() {
        let input_list = ['reference', 'refRev', 'prodDraw', 'prodDrawRev', 'alias', 'refTitleFra', 'action', 'docType', 'materialType', 'inventoryImpact', 'ex', 'custDrawing', 'custDrawingRev', 'weight', 'weightUnit', 'material', 'platingSurface', 'platingSurfaceUnit', 'eccn', 'rdo', 'hts', 'comments'];
        var required = true;
        for (let i = 0; i < input_list.length; i++) {
            if ($('#' + input_list[i]).prop('required') && $('#' + input_list[i]).val() === "") {
                Toast.fire({
                    icon: 'error',
                    title: 'Veuillez remplir tous les champs obligatoires !'
                });
                required = false;
                if ($('#' + input_list[i]).hasClass('selectpicker')) {
                    $('#' + input_list[i]).next().addClass('border-danger');
                } else {
                    $('#' + input_list[i]).addClass('border-danger');
                }
            } else {
                if ($('#' + input_list[i]).hasClass('selectpicker')) {
                    $('#' + input_list[i]).next().removeClass('border-danger');
                } else {
                    $('#' + input_list[i]).removeClass('border-danger');
                }
            }
        }
        return required;
    }

    function collect_data_document() {
        let reference = $('#reference').val();
        let refRev = $('#refRev').val();
        let prodDraw = $('#prodDraw').val();
        let prodDrawRev = $('#prodDrawRev').val();
        let alias = $('#alias').val();
        let refTitleFra = $('#refTitleFra').val();
        let action = $('#action').val();
        let docType = $('#docType').val();
        let materialType = $('#materialType').val();
        let inventoryImpact = $('#inventoryImpact').val();
        let ex = $('#ex').val();
        let custDrawing = $('#custDrawing').val();
        let custDrawingRev = $('#custDrawingRev').val();
        let weight = $('#weight').val();
        let weightUnit = $('#weightUnit').val();
        let materials = [];
        // Récupérer les matériaux sélectionnés
        $('#materials-container input[name="materials[]"]').each(function() {
            materials.push($(this).val());
        });
        let platingSurface = $('#platingSurface').val();
        let platingSurfaceUnit = $('#platingSurfaceUnit').val();
        let internalMachRec = $('#internalMachRec').is(':checked') ? 1 : 0;
        let eccn = $('#eccn').val();
        let rdo = $('#rdo').val();
        let hts = $('#hts').val();
        let comments = $('#comments').val();
        return {
            reference: reference,
            refRev: refRev,
            prodDraw: prodDraw,
            prodDrawRev: prodDrawRev,
            alias: alias,
            refTitleFra: refTitleFra,
            action: action,
            docType: docType,
            materialType: materialType,
            inventoryImpact: inventoryImpact,
            ex: ex,
            custDrawing: custDrawing,
            custDrawingRev: custDrawingRev,
            weight: weight,
            weightUnit: weightUnit,
            materials: materials,
            platingSurface: platingSurface,
            platingSurfaceUnit: platingSurfaceUnit,
            internalMachRec: internalMachRec,
            eccn: eccn,
            rdo: rdo,
            hts: hts,
            comments: comments,
        };
    }

    function empty_fields() {
        $('#reference').val('');
        $('#refRev').val('');
        $('#prodDraw').val('');
        $('#prodDrawRev').val('');
        $('#alias').val('');
        updateCharCount(); // Mettre à jour le compteur après avoir vidé le champ
        $('#refTitleFra').val('');
        updateTitleCharCount(); // Mettre à jour le compteur après avoir vidé le champ
        $('#action').selectpicker('val', '');
        $('#docType').selectpicker('val', '');
        $('#internalMachRec').prop('checked', false);
        $('#materialType').selectpicker('val', '');
        $('#inventoryImpact').selectpicker('val', '');
        $('#ex').selectpicker('val', '');
        $('#custDrawing').val('');
        $('#custDrawingRev').val('');
        $('#weight').val('');
        $('#weightUnit').selectpicker('val', '');
        $('#material').selectpicker('val', '');
        $('#platingSurface').val('');
        $('#platingSurfaceUnit').selectpicker('val', '');
        $('#eccn').val('');
        $('#rdo').val('');
        $('#hts').val('');
        $('#comments').val('');
        refreshIframe();

        // Masquer le bouton des commentaires antérieurs
        updatePreviousCommentsButton(null);
    }


    function change_border(input) {
        if (input.prop('required') && input.val() === "") {
            if (input.hasClass('selectpicker')) {
                input.next().addClass('border-danger');
            } else {
                input.addClass('border-danger');
            }
        } else {
            if (input.hasClass('selectpicker')) {
                input.next().removeClass('border-danger');
            } else {
                input.removeClass('border-danger');
            }
        }
    }

    $('input, select').change(function() {
        change_border($(this));
    });

    // Fonction pour sauvegarder un commentaire si il y en a un
    function saveCommentIfExists() {
        const commentText = $('#comments').val().trim();
        const documentId = $('#document_id').val();

        if (!commentText || !documentId) {
            return Promise.resolve(); // Pas de commentaire à sauvegarder
        }

        return new Promise((resolve, reject) => {
            $.ajax({
                url: "{{ path('app_commentaire_new') }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    documentId: documentId,
                    content: commentText,
                    type: 'principal',
                    state: 'package'
                }),
                success: function(response) {
                    // Vider le champ commentaire après sauvegarde
                    $('#comments').val('');
                    resolve(response);
                },
                error: function(xhr) {
                    reject(xhr);
                }
            });
        });
    }


    function refresh_table_documents(){
        $.ajax({
            url: "{{ path('verif_package', {'id': package.id}) }}",
            type: 'POST',
            success: function(response){
                $('#div-documents').html($(response).find('#div-documents').html());
                $(function () {
                    $('[data-bs-toggle="tooltip"]').tooltip()
                })
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour des documents !'
                });
            }
        });
    }


    function fill_fields_document(){
        let row = $(this);
        if (row.hasClass('selected-row')) {
            empty_fields();
            row.removeClass('selected-row');
            $('#add-document').show();
            $('#delete-document').hide();
            return;
        }
        let id = row.attr('id').split('_')[1];
        let reference = row.find('.reference').text();
        let refRev = row.find('.refRev').text();
        let prodDraw = row.find('.prodDraw').text();
        let prodDrawRev = row.find('.prodDrawRev').text();
        let alias = row.find('.alias').text();
        let refTitleFra = row.find('.refTitleFra').text();
        let action = row.find('.action').text();
        let docType = row.find('.docType').text();
        let internalMachRec = row.find('.internalMachRec').find('img').length ? 1 : 0;
        let materialType = row.find('.materialType').text();
        let inventoryImpact = row.find('.inventoryImpact').text();
        let ex = row.find('.ex').text().trim();
        let custDrawing = row.find('.custDrawing').text();
        let custDrawingRev = row.find('.custDrawingRev').text();
        let weight = row.find('.weight').text();
        let weightUnit = row.find('.weightUnit').text();
        let material = row.find('.material').text();
        let platingSurface = row.find('.platingSurface').text();
        let platingSurfaceUnit = row.find('.platingSurfaceUnit').text();
        let eccn = row.find('.eccn').text();
        let rdo = row.find('.rdo').text();
        let hts = row.find('.hts').text();
        let comments = row.find('.comments').find('i').attr('data-bs-title');

        $('#document_id').val(id);
        $('#reference').val(reference);
        $('#refRev').val(refRev);
        $('#prodDraw').val(prodDraw);
        $('#prodDrawRev').val(prodDrawRev);
        $('#alias').val(alias);
        updateCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#refTitleFra').val(refTitleFra);
        updateTitleCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#action').selectpicker('val', action);
        $('#docType').selectpicker('val', docType);
        $('#internalMachRec').prop('checked', internalMachRec);
        $('#materialType').selectpicker('val', materialType);
        $('#inventoryImpact').selectpicker('val', inventoryImpact);
        $('#ex').selectpicker('val', ex);
        $('#custDrawing').val(custDrawing);
        $('#custDrawingRev').val(custDrawingRev);
        $('#weight').val(weight);
        $('#weightUnit').selectpicker('val', weightUnit);
        $('#material').selectpicker('val', material);
        $('#platingSurface').val(platingSurface);
        $('#platingSurfaceUnit').selectpicker('val', platingSurfaceUnit);
        $('#eccn').val(eccn);
        $('#rdo').val(rdo);
        $('#hts').val(hts);
        $('#comments').val(comments);

        $('.document_row').removeClass('selected-row');
        row.addClass('selected-row');

        $('#add-document').hide();
        $('#delete-document').show();
        refreshIframe();

        // Charger le nombre de commentaires pour mettre à jour le bouton
        loadCommentsCount(id);
    }


    $(document).on('click', '.document_row', function(e) {
        if ($(e.target).closest('.copy_icone').length) {
            copy_icone.call(this);
            return;
        }
        fill_fields_document.call(this, e);
    });


    function copy_icone(){
        empty_fields();
        let row = $(this).closest('.document_row');
        let reference = row.find('.reference').text();
        let refRev = row.find('.refRev').text();
        let prodDraw = row.find('.prodDraw').text();
        let prodDrawRev = row.find('.prodDrawRev').text();
        let alias = row.find('.alias').text();
        let refTitleFra = row.find('.refTitleFra').text();
        let action = row.find('.action').text();
        let docType = row.find('.docType').text();
        let internalMachRec = row.find('.internalMachRec').length ? 1 : 0;
        let materialType = row.find('.materialType').text();
        let inventoryImpact = row.find('.inventoryImpact').text();
        let ex = row.find('.ex').text();
        let custDrawing = row.find('.custDrawing').text();
        let custDrawingRev = row.find('.custDrawingRev').text();
        let weight = row.find('.weight').text();
        let weightUnit = row.find('.weightUnit').text();
        let material = row.find('.material').text();
        let platingSurface = row.find('.platingSurface').text();
        let platingSurfaceUnit = row.find('.platingSurfaceUnit').text();
        let eccn = row.find('.eccn').text();
        let rdo = row.find('.rdo').text();
        let hts = row.find('.hts').text();
        let comments = row.find('.comments').find('i').attr('data-bs-title');

        $('#reference').val(reference);
        $('#refRev').val(refRev);
        $('#prodDraw').val(prodDraw);
        $('#prodDrawRev').val(prodDrawRev);
        $('#alias').val(alias);
        updateCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#refTitleFra').val(refTitleFra);
        updateTitleCharCount(); // Mettre à jour le compteur après avoir défini la valeur
        $('#action').selectpicker('val', action);
        $('#docType').selectpicker('val', docType);
        $('#internalMachRec').prop('checked', internalMachRec);
        $('#materialType').selectpicker('val', materialType);
        $('#inventoryImpact').selectpicker('val', inventoryImpact);
        $('#ex').selectpicker('val', ex);
        $('#custDrawing').val(custDrawing);
        $('#custDrawingRev').val(custDrawingRev);
        $('#weight').val(weight);
        $('#weightUnit').selectpicker('val', weightUnit);
        $('#material').selectpicker('val', material);
        $('#platingSurface').val(platingSurface);
        $('#platingSurfaceUnit').selectpicker('val', platingSurfaceUnit);
        $('#eccn').val(eccn);
        $('#rdo').val(rdo);
        $('#hts').val(hts);
        $('#comments').val(comments);

        refreshIframe();
        Toast.fire({
            icon: 'success',
            title: 'Données copiées !'
        });
    };


    $(document).on('click', '.copy_icone', function(e){
        copy_icone();
    });


    function refuser_package(){
        $.ajax({
            url: "{{ path('refuser_package', {'id': package.id}) }}",
            type: 'POST',
            success: function(response){
                Toast.fire({
                    icon: 'success',
                    title: 'Package renvoyé !'
                });
                window.location.href = "{{ path('app_package') }}";
            },
            error: function(response){
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du renvoi du package !'
                });
            }
        });
    }

    $('#refuser').click(function(){
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Vous ne pourrez pas revenir en arrière !",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, renvoyer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            },
        }).then((result) => {
            if (result.isConfirmed) {
                refuser_package();
            }
        });
    });

    // Variable globale pour stocker l'ID du document actuel
    let currentDocumentId = null;

    // Variable globale pour stocker l'ID du document actuellement chargé dans le formulaire
    let currentFormDocumentId = null;

    // Fonction pour afficher le modal des commentaires
    function showCommentsModal(documentId, documentReference) {
        // Stocker l'ID du document pour l'ajout de commentaires
        currentDocumentId = documentId;

        // Mettre à jour le titre du modal
        document.getElementById('documentReference').textContent = documentReference;

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('modalComments'));
        modal.show();

        // Charger les commentaires
        loadCommentsForModal(documentId);
    }

    // Fonction pour ajouter un nouveau commentaire
    function addNewComment() {
        const commentTextarea = document.getElementById('newCommentText');
        const commentText = commentTextarea.value.trim();

        if (!commentText) {
            Toast.fire({
                icon: 'warning',
                title: 'Veuillez saisir un commentaire'
            });
            return;
        }

        if (!currentDocumentId) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur: Document non identifié'
            });
            return;
        }

        // Désactiver le formulaire pendant l'envoi
        const form = document.querySelector('.modal-footer');
        form.classList.add('comment-form-loading');
        document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Ajout...';

        // Appeler l'API pour ajouter le commentaire
        $.ajax({
            url: "{{ path('app_commentaire_new') }}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                documentId: currentDocumentId,
                content: commentText,
                type: 'principal',
                state: 'package'
            }),
            success: function(response) {
                form.classList.remove('comment-form-loading');
                document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

                // Vider le champ de commentaire
                commentTextarea.value = '';

                Toast.fire({
                    icon: 'success',
                    title: 'Commentaire ajouté'
                });

                // Recharger les commentaires dans le modal après succès
                setTimeout(() => {
                    loadCommentsForModal(currentDocumentId);
                }, 200);
            },
            error: function(xhr) {
                form.classList.remove('comment-form-loading');
                document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de l\'ajout du commentaire'
                });
            }
        });
    }

    // Fonction séparée pour charger les commentaires
    function loadCommentsForModal(documentId) {
        document.getElementById('commentsModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2 text-muted">Chargement des commentaires...</p>
            </div>
        `;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                displayComments(comments);
            },
            error: function(xhr, status, error) {
                document.getElementById('commentsModalBody').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des commentaires: ${error}
                    </div>
                `;
            }
        });
    }

    // Fonction pour afficher les commentaires dans le modal
    function displayComments(comments) {
        const modalBody = document.getElementById('commentsModalBody');

        if (!comments || comments.length === 0) {
            modalBody.innerHTML = `
                <div class="no-comments">
                    <i class="fas fa-comment-slash fa-3x mb-3" style="color: #ccc;"></i>
                    <p>Aucun commentaire pour ce document.</p>
                </div>
            `;
            return;
        }

        let html = `<div class="comments-count">
            <i class="fas fa-comments me-2"></i>
            ${comments.length} commentaire${comments.length > 1 ? 's' : ''}
        </div>`;

        comments.forEach(comment => {
            const createdAt = new Date(comment.created_at);
            const formattedDate = createdAt.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            html += `
                <div class="comment-item">
                    <div class="comment-header">
                        <span class="comment-state">${comment.state ? comment.state.toUpperCase() : 'GÉNÉRAL'}</span>
                        <div class="comment-meta">
                            <i class="fas fa-user me-1"></i>
                            ${comment.user ? comment.user.prenom + ' ' + comment.user.nom : 'Utilisateur inconnu'}
                            <i class="fas fa-clock ms-3 me-1"></i>
                            ${formattedDate}
                        </div>
                    </div>
                    <div class="comment-content">
                        ${comment.commentaire}
                    </div>
                </div>
            `;
        });

        modalBody.innerHTML = html;
    }

    // Affiche/Recharge le tooltip au survol
    function loadCommentsTooltip(iconElem) {
        const documentId = $(iconElem).data('document-id');
        const txt = $(iconElem).attr('txt') || '<em>Aucun commentaire</em>';

        // Mettre à jour le tooltip avec le contenu
        $(iconElem).attr('data-bs-original-title', txt);

        // Réinitialiser le tooltip
        const tooltip = bootstrap.Tooltip.getInstance(iconElem);
        if (tooltip) {
            tooltip.dispose();
        }
        new bootstrap.Tooltip(iconElem, {
            html: true,
            placement: 'top'
        });
    }

    // Fonction pour afficher les commentaires antérieurs du document actuellement chargé dans le formulaire
    function showPreviousCommentsForCurrentDocument() {
        if (!currentFormDocumentId) {
            Toast.fire({
                icon: 'warning',
                title: 'Aucun document sélectionné'
            });
            return;
        }

        // Utiliser la fonction existante du modal avec l'ID du document du formulaire
        const reference = $('#reference').val() || 'Document';
        showCommentsModal(currentFormDocumentId, reference);
    }

    // Fonction pour mettre à jour le bouton des commentaires antérieurs
    function updatePreviousCommentsButton(documentId, commentsCount = 0) {
        currentFormDocumentId = documentId;
        const button = $('#viewPreviousCommentsBtn');
        const countSpan = $('#commentsCount');

        if (documentId && commentsCount > 0) {
            button.show();
            countSpan.text(commentsCount);
        } else if (documentId) {
            // Afficher le bouton même s'il n'y a pas de commentaires pour permettre d'en ajouter
            button.show();
            countSpan.text(0);
        } else {
            button.hide();
            currentFormDocumentId = null;
        }
    }

    // Fonction pour charger le nombre de commentaires d'un document
    function loadCommentsCount(documentId) {
        if (!documentId) return;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                const count = comments ? comments.length : 0;
                updatePreviousCommentsButton(documentId, count);
            },
            error: function(xhr, status, error) {
                // En cas d'erreur, afficher quand même le bouton
                updatePreviousCommentsButton(documentId, 0);
            }
        });
    }

    // Rendre les fonctions globales pour qu'elles soient accessibles depuis le HTML
    window.showCommentsModal = showCommentsModal;
    window.addNewComment = addNewComment;
    window.loadCommentsTooltip = loadCommentsTooltip;
    window.showPreviousCommentsForCurrentDocument = showPreviousCommentsForCurrentDocument;

</script>

<!-- Scripts pour les matériaux -->
<script src="{{ asset('js/material-datalist.js') }}"></script>
<script src="{{ asset('js/material-tags.js') }}"></script>

<script>
    // Variable globale pour l'instance des matériaux
    window.materialTagsInstance = null;

    // Fonction pour charger les matériaux d'un document
    function loadDocumentMaterials(documentId) {
        $.ajax({
            url: "{{ path('api_document_materials', {'id': 'DOCUMENT_ID'}) }}".replace('DOCUMENT_ID', documentId),
            type: 'GET',
            success: function(materials) {
                if (window.materialTagsInstance && materials.length > 0) {
                    window.materialTagsInstance.setMaterials(materials);
                }
            },
            error: function(xhr, status, error) {
                console.error('Erreur lors du chargement des matériaux:', error);
            }
        });
    }

    // Initialiser le système de matériaux après le chargement de la page
    $(document).ready(function() {
        // Initialiser le composant de tags de matériaux
        window.materialTagsInstance = new MaterialTags(document.getElementById('materials-container'), {
            inputName: 'materials[]',
            placeholder: 'Rechercher un matériau...',
            maxTags: 5
        });

        // Écouter les événements d'ajout/suppression de matériaux
        document.getElementById('materials-container').addEventListener('materialAdded', function(e) {
            console.log('Matériau ajouté:', e.detail);
        });

        document.getElementById('materials-container').addEventListener('materialRemoved', function(e) {
            console.log('Matériau supprimé:', e.detail);
        });
    });
</script>
{% endblock %}
